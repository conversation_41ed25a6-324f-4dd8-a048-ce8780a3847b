第3周 7.28-8.3
周总结
- 做对了什么
- 收获了什么经验
- 做错了什么
- 有哪些要避免的教训
本周顺利地完成了从imu识别到关节映射修改到姿态校准再到人物模型根据imu数据渲染更新的整个完整的过程。顺利地完成了上周所开发的一些小模块的融合，但是在具体的问题逻辑和细节的优化上遇到了很多的问题，需要重新思考整理如何减少bug的出现，比如我手动调整关节姿态后，我的人物模型初始化姿态应该以什么状态呈现，用户的修改什么时候保留什么时候舍弃等等一系列面向用户的开发优化。

7月28日
主要工作产出
1. 遇到了DLL链接出错的bug，有如下几个猜测：
- C++运行库出错
- QApplation和渲染界面的加载冲突
当前这个问题并没有解决
明日计划
[x] 打通四元数控制和模型渲染的问题

---

7月29日
主要工作产出
1. DOGlove手套硬件测试
  - 电路板电源连接测试 - 只要5V电压即可
  - 固件烧录：使用STM32CubeProgrammer，通过LINK-VK进行DOglove.hex烧录
  - 解决电路板电源连接，测试电路板状态灯
[图片]
明日计划
[x] 打通四元数控制和模型渲染的问题

---
7月30日
主要工作产出
1. model-control完成，当前可以顺利渲染出mujoco模型，并且通过控件设置四元数对于模型进行操控，ui也完成优化，用户友好。这一版代码已上传Gitee仓库：https://gitee.com/hang-yang233/model-control
  1. 完成模型渲染的各种问题
    1. 模型固定
    2. 物理引擎
    3. 关节映射
    4. xml分辨率修改
[图片]
明日计划
[] 尝试整合当前所有代码

---
7月31日
主要工作产出
1. 尝试把imu读取和模型控制集成
  1. 完成模型T姿态校准
[图片]
2. 阅读一篇论文，基于粗细粒度的弱监督功能性抓取，对物品进行类别的划分通过粗度预测抓取姿态和细度精细化，根据一种弱监督的方法来从手动 - 对象相互作用的Exentric（EXO）图像中提取相关线索，形成了GAAF-DEX框架，可以学习粒度感知到的人类对象相互作用，从而生成功能抓取姿态。
明日计划
[x] 尝试整合当前所有代码

---
8月1日
主要工作产出
1. 尝试把imu读取和模型控制集成
  1. 完成imu的识别
  2. 完成关节映射修改
  3. 完成T姿态校准
  4. 完成手动修改关节角度值
  5. 完成动态渲染关节以及imu的数据值
[图片]
明日计划
[x] 尝试整合当前所有代码

---