import numpy as np
import matplotlib.pyplot as plt

# 激活函数实现
def sigmoid(z):
    """Sigmoid激活函数"""
    z = np.clip(z, -500, 500)  # 防止溢出
    return 1 / (1 + np.exp(-z))

def relu(z):
    """ReLU激活函数"""
    return np.maximum(0, z)

def relu_derivative(z):
    """ReLU导数"""
    return (z > 0).astype(float)

def tanh(z):
    """Tanh激活函数"""
    return np.tanh(z)

# 神经网络类实现
class NeuralNetwork:
    def __init__(self, layer_sizes):
        """
        初始化神经网络
        layer_sizes: 各层神经元数量，如[784, 25, 10]
        """
        self.layer_sizes = layer_sizes
        self.num_layers = len(layer_sizes)
        
        # 权重和偏置初始化
        self.weights = {}
        self.biases = {}
        
        for i in range(1, self.num_layers):
            # He初始化，适用于ReLU
            self.weights[f'W{i}'] = np.random.randn(layer_sizes[i], layer_sizes[i-1]) * np.sqrt(2.0/layer_sizes[i-1])
            self.biases[f'b{i}'] = np.zeros((layer_sizes[i], 1))
    
    def forward_propagation(self, X):
        """前向传播"""
        cache = {'A0': X}  # 存储中间结果
        A = X
        
        for i in range(1, self.num_layers):
            W = self.weights[f'W{i}']
            b = self.biases[f'b{i}']
            
            Z = np.dot(W, A) + b
            cache[f'Z{i}'] = Z
            
            # 最后一层用sigmoid，其他层用ReLU
            if i == self.num_layers - 1:
                A = sigmoid(Z)
            else:
                A = relu(Z)
            
            cache[f'A{i}'] = A
        
        return A, cache
    
    def compute_cost(self, AL, Y):
        """计算损失函数"""
        m = Y.shape[1]
        
        # 防止log(0)
        AL = np.clip(AL, 1e-15, 1-1e-15)
        
        # 交叉熵损失
        cost = -np.sum(Y * np.log(AL) + (1-Y) * np.log(1-AL)) / m
        
        return cost
    
    def backward_propagation(self, cache, Y):
        """反向传播"""
        gradients = {}
        m = Y.shape[1]
        L = self.num_layers - 1  # 最后一层的索引
        
        # 输出层梯度
        AL = cache[f'A{L}']
        dZL = AL - Y
        gradients[f'dW{L}'] = np.dot(dZL, cache[f'A{L-1}'].T) / m
        gradients[f'db{L}'] = np.sum(dZL, axis=1, keepdims=True) / m
        
        # 隐藏层梯度（反向传播）
        dA = np.dot(self.weights[f'W{L}'].T, dZL)
        
        for i in range(L-1, 0, -1):
            dZ = dA * relu_derivative(cache[f'Z{i}'])
            gradients[f'dW{i}'] = np.dot(dZ, cache[f'A{i-1}'].T) / m
            gradients[f'db{i}'] = np.sum(dZ, axis=1, keepdims=True) / m
            
            if i > 1:
                dA = np.dot(self.weights[f'W{i}'].T, dZ)
        
        return gradients
    
    def update_parameters(self, gradients, learning_rate):
        """更新参数"""
        for i in range(1, self.num_layers):
            self.weights[f'W{i}'] -= learning_rate * gradients[f'dW{i}']
            self.biases[f'b{i}'] -= learning_rate * gradients[f'db{i}']
    
    def train(self, X, Y, learning_rate=0.01, num_iterations=1000, print_cost=True):
        """训练神经网络"""
        costs = []
        
        for i in range(num_iterations):
            # 前向传播
            AL, cache = self.forward_propagation(X)
            
            # 计算损失
            cost = self.compute_cost(AL, Y)
            costs.append(cost)
            
            # 反向传播
            gradients = self.backward_propagation(cache, Y)
            
            # 更新参数
            self.update_parameters(gradients, learning_rate)
            
            # 打印损失
            if print_cost and i % 100 == 0:
                print(f"Cost after iteration {i}: {cost}")
        
        return costs
    
    def predict(self, X):
        """预测"""
        AL, _ = self.forward_propagation(X)
        predictions = (AL > 0.5).astype(int)
        return predictions

# 简单的二分类数据生成
def generate_data(m=300):
    """生成简单的二分类数据"""
    np.random.seed(1)
    
    # 生成两个类别的数据
    X1 = np.random.randn(2, m//2) + np.array([[2], [2]])
    X2 = np.random.randn(2, m//2) + np.array([[-2], [-2]])
    
    X = np.hstack([X1, X2])
    Y = np.hstack([np.ones((1, m//2)), np.zeros((1, m//2))])
    
    return X, Y

# 测试代码
if __name__ == "__main__":
    # 生成数据
    X, Y = generate_data(400)
    
    # 创建神经网络 [输入层2个神经元, 隐藏层4个神经元, 输出层1个神经元]
    nn = NeuralNetwork([2, 4, 1])
    
    # 训练网络
    print("开始训练神经网络...")
    costs = nn.train(X, Y, learning_rate=0.5, num_iterations=2000)
    
    # 预测
    predictions = nn.predict(X)
    accuracy = np.mean(predictions == Y) * 100
    print(f"训练集准确率: {accuracy:.2f}%")
    
    # 绘制损失曲线
    plt.figure(figsize=(10, 6))
    plt.plot(costs)
    plt.title('神经网络训练损失曲线')
    plt.xlabel('迭代次数 (每100次)')
    plt.ylabel('损失值')
    plt.grid(True)
    plt.show()
    
    # 可视化决策边界
    def plot_decision_boundary(X, Y, model):
        plt.figure(figsize=(10, 8))
        
        # 设置网格
        h = 0.01
        x_min, x_max = X[0, :].min() - 1, X[0, :].max() + 1
        y_min, y_max = X[1, :].min() - 1, X[1, :].max() + 1
        xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                            np.arange(y_min, y_max, h))
        
        # 预测网格点
        grid_points = np.c_[xx.ravel(), yy.ravel()].T
        Z, _ = model.forward_propagation(grid_points)
        Z = Z.reshape(xx.shape)
        
        # 绘制决策边界
        plt.contour(xx, yy, Z, levels=[0.5], colors='red', linestyles='--', linewidths=2)
        
        # 绘制数据点
        plt.scatter(X[0, Y[0]==1], X[1, Y[0]==1], c='blue', marker='o', label='类别1')
        plt.scatter(X[0, Y[0]==0], X[1, Y[0]==0], c='red', marker='s', label='类别0')
        
        plt.xlabel('特征1')
        plt.ylabel('特征2')
        plt.title('神经网络决策边界')
        plt.legend()
        plt.grid(True)
        plt.show()
    
    # 绘制决策边界
    plot_decision_boundary(X, Y, nn)