第1周 7.14-7.20
周总结
- 做对了什么
- 收获了什么经验
- 做错了什么
- 有哪些要避免的教训
目前采用的是PyQt5 + OpenGL + 数据处理库 + FBX SDK，并采用git进行版本管理，经过前期调研形成的方案，实测确实可行。在细节上仍然存在问题，需要进一步优化。加深了对于人体关节的控制，人对于可视化是更加敏感的，对于数据可以添加更多的可视化方法，帮助理解和处理。对于git版本的使用比较舒服，在形成一个比较稳定的小版本，push一下代码库，之后方便版本的回滚和更新。


7月17日
主要工作产出
1. imu_sdk 优化：导出身体四元数动作数据
[图片]
2. 整理windows系统高速手和通用手的使用教程
3. imu FBX渲染：编写接收四元数的函数
  1. 身体模型节点和四元数的位置不对应
明日计划
[x] 解决imu FBX渲染身体模型节点和四元数的位置不对应问题
[] 尝试将IMU SDK代码集成到一块

---
7月18日
主要工作产出
1. imu_sdk 优化：添加校准imu模块，防止数据初始位置偏移
[图片]
2. 整理产品测试问卷
3. imu FBX渲染：优化渲染的帧率，添加插帧补偿
  1. 身体模型节点为刚性，四元数直接渲染会导致部位脱离身体
[图片]
明日计划
[] 尝试将IMU SDK代码集成到一块


初次之外，学习完吴恩达机器学习第一周及相关代码