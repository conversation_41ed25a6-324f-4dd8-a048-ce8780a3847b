# 12DOF到16DOF手部运动映射系统 - 项目结构说明

## 📁 项目目录结构

```
hand-motion-mapping/
├── 📄 README.md                           # 项目说明文档
├── 📄 系统说明.md                         # 详细系统说明
├── 📄 12DOF到15DOF手部运动映射技术方案.md  # 技术方案文档
├── 📄 项目结构说明.md                     # 本文件
├── 📄 requirements.txt                    # Python依赖包列表
├── 📄 config.yaml                         # 系统配置文件
├── 📄 main.py                             # 系统主程序入口
├── 📄 deploy.sh                           # 自动部署脚本
│
├── 📂 src/                                # 源代码目录
│   ├── 📄 __init__.py                     # 包初始化文件
│   ├── 📄 utils.py                        # 工具函数和配置管理
│   ├── 📄 sensor_interface.py             # 传感器接口模块
│   ├── 📄 dip_estimator.py                # DIP关节估算器
│   ├── 📄 slsqp_optimizer.py              # SLSQP优化器
│   ├── 📄 motion_mapper.py                # 运动映射核心模块
│   └── 📄 real_time_controller.py         # 实时控制器
│
├── 📂 tests/                              # 测试目录
│   ├── 📄 __init__.py                     # 测试包初始化
│   ├── 📄 test_system.py                  # 系统集成测试
│   └── 📂 data/                           # 测试数据目录
│
├── 📂 logs/                               # 日志目录（运行时创建）
│   ├── 📄 system.log                      # 系统日志
│   ├── 📄 performance.log                 # 性能日志
│   └── 📄 daemon.log                      # 守护进程日志
│
├── 📂 data/                               # 数据目录
│   └── 📂 recordings/                     # 数据记录目录
│
├── 📂 models/                             # 模型目录
│   ├── 📄 dip_estimator.pth               # DIP估算神经网络模型
│   └── 📄 dip_estimator_scaler.pkl        # 数据标准化参数
│
├── 📂 config/                             # 配置目录
│   ├── 📄 sensor_calibration.yaml         # 传感器校准参数
│   └── 📂 backup/                         # 配置备份目录
│
└── 📂 plots/                              # 可视化图表目录（可选）
```

## 📋 文件详细说明

### 🔧 核心配置文件

#### `config.yaml`
系统主配置文件，包含所有模块的配置参数：

```yaml
# 系统基本配置
system:
  name: "Hand Motion Mapping System"
  version: "1.0.0"
  debug_mode: false

# 传感器配置
sensor:
  interface_type: "fiber_optic"
  port: "/dev/ttyUSB0"
  baudrate: 115200
  sampling_rate: 100

# 机器人手配置
robot_hand:
  dof_count: 16
  joint_names: [...]
  joint_limits: {...}

# DIP估算器配置
dip_estimator:
  biomechanical: {...}
  ml_model: {...}
  fusion: {...}

# SLSQP优化器配置
slsqp_optimizer:
  ftol: 1.0e-6
  maxiter: 100
  weights: {...}

# 实时控制器配置
real_time_controller:
  target_frequency: 50
  max_computation_time: 0.01
  safety: {...}

# 性能监控配置
performance_monitor:
  enable: true
  metrics_window_size: 1000
  thresholds: {...}
```

#### `requirements.txt`
Python依赖包列表：

```txt
# 核心计算库
numpy>=1.21.0
scipy>=1.7.0
nlopt>=2.7.0

# 机器学习框架
torch>=1.12.0
scikit-learn>=1.1.0

# 数据处理
pandas>=1.4.0
h5py>=3.7.0

# 配置管理
PyYAML>=6.0
configargparse>=1.5.0

# 实时系统
psutil>=5.9.0

# 机器人学库
pinocchio>=2.6.0

# 通信和接口
pyserial>=3.5

# 日志和监控
loguru>=0.6.0

# 测试框架
pytest>=7.1.0
pytest-cov>=3.0.0
```

### 🚀 主程序文件

#### `main.py`
系统主入口程序，提供以下功能：

- **命令行参数解析**: 支持配置文件路径、运行模式等参数
- **系统初始化**: 加载配置、创建组件实例
- **交互模式**: 提供命令行交互界面
- **守护进程模式**: 后台运行支持
- **测试模式**: 系统功能验证
- **性能监控**: 实时性能统计和报告

主要命令行选项：
```bash
python main.py                    # 交互模式
python main.py --daemon           # 守护进程模式
python main.py --config custom.yaml  # 自定义配置
python main.py --test             # 测试模式
python main.py --verbose          # 详细输出
```

### 📦 核心源代码模块

#### `src/utils.py`
工具函数和基础设施模块：

**主要类**：
- `ConfigManager`: 配置文件管理，支持嵌套键访问
- `Logger`: 多级别日志记录，支持文件和控制台输出
- `PerformanceMonitor`: 实时性能监控和统计

**主要函数**：
- `create_directories()`: 创建必要的目录结构
- `validate_config()`: 验证配置文件有效性
- `degrees_to_radians()`: 角度弧度转换
- `apply_joint_limits()`: 关节限制应用
- `smooth_trajectory()`: 轨迹平滑处理

#### `src/sensor_interface.py`
传感器接口模块：

**主要类**：
- `SensorInterface`: 传感器接口抽象基类
- `FiberOpticSensor`: 光纤传感器具体实现
- `DataPreprocessor`: 数据预处理器
- `SensorData`: 传感器数据结构

**核心功能**：
- 串口通信管理
- 实时数据采集（100Hz）
- 数字滤波（Butterworth + Kalman）
- 异常值检测和处理
- 数据质量评估

#### `src/dip_estimator.py`
DIP关节估算器模块：

**主要类**：
- `DIPEstimator`: 主估算器，融合多种方法
- `BiomechanicalModel`: 生物力学模型估算
- `MLModel`: 机器学习模型估算
- `DIPEstimation`: 估算结果数据结构

**估算策略**：
- 生物力学模型：基于DIP/PIP比例关系
- 深度学习模型：12输入→4输出神经网络
- 融合策略：自适应权重调整
- 时间平滑：历史数据辅助

#### `src/slsqp_optimizer.py`
SLSQP优化器模块：

**主要类**：
- `SLSQPOptimizer`: 主优化器类
- `ForwardKinematics`: 正向运动学计算
- `OptimizationResult`: 优化结果数据结构
- `KeypointVector`: 关键点向量定义

**优化目标**：
- 关键点匹配误差最小化
- 时间连续性约束
- 生物力学约束
- 关节限制约束
- 碰撞避免约束

#### `src/motion_mapper.py`
运动映射核心模块：

**主要类**：
- `MotionMapper`: 核心映射类
- `HumanHandModel`: 人手运动学模型
- `MappingResult`: 映射结果数据结构

**分层映射流程**：
1. 直接映射：12DOF传感器数据→已知关节
2. DIP估算：估算缺失的4个DIP关节
3. 全局优化：SLSQP优化完整16DOF输出

#### `src/real_time_controller.py`
实时控制器模块：

**主要类**：
- `RealTimeController`: 主控制器
- `SafetyMonitor`: 安全监控器
- `ControllerState`: 控制器状态枚举
- `SystemStatus`: 系统状态数据结构

**核心功能**：
- 实时控制循环（50Hz+）
- 安全监控和紧急停止
- 错误处理和恢复
- 性能监控和统计
- 多线程管理

### 🧪 测试模块

#### `tests/test_system.py`
系统集成测试：

**测试类**：
- `TestSystemIntegration`: 系统集成测试
- `TestSystemBenchmark`: 性能基准测试
- `MockSensorInterface`: 模拟传感器接口

**测试覆盖**：
- 配置加载验证
- 传感器接口测试
- DIP估算精度测试
- SLSQP优化性能测试
- 运动映射完整性测试
- 实时性能基准测试
- 错误处理验证
- 时间平滑性测试

### 🔧 部署和运维

#### `deploy.sh`
自动部署脚本：

**功能模块**：
- 系统要求检查
- Python虚拟环境创建
- 依赖包安装
- 目录结构创建
- 系统配置
- 测试运行
- 启动脚本生成
- systemd服务创建

**使用方法**：
```bash
chmod +x deploy.sh
./deploy.sh                # 完整部署
./deploy.sh --skip-tests   # 跳过测试
```

## 🔄 数据流程图

```
传感器数据 (12DOF)
    ↓
数据预处理 (滤波、验证)
    ↓
分层映射器
    ├── 直接映射 (已知12关节)
    ├── DIP估算 (4个DIP关节)
    └── SLSQP优化 (全局优化)
    ↓
安全检查 (关节限制、速度限制)
    ↓
机械手控制信号 (16DOF)
```

## 📊 模块依赖关系

```
main.py
    ├── utils.py (ConfigManager, Logger, PerformanceMonitor)
    ├── sensor_interface.py (FiberOpticSensor)
    ├── motion_mapper.py
    │   ├── dip_estimator.py (DIPEstimator)
    │   └── slsqp_optimizer.py (SLSQPOptimizer)
    └── real_time_controller.py
        ├── motion_mapper.py
        ├── sensor_interface.py
        └── utils.py (SafetyMonitor)
```

## 🚀 快速开发指南

### 添加新的传感器类型

1. 在 `sensor_interface.py` 中继承 `SensorInterface`
2. 实现必要的抽象方法
3. 在配置文件中添加新类型支持
4. 更新工厂方法

### 添加新的优化算法

1. 在 `slsqp_optimizer.py` 中创建新的优化器类
2. 实现优化接口
3. 在 `motion_mapper.py` 中集成
4. 添加相应的配置选项

### 扩展DIP估算方法

1. 在 `dip_estimator.py` 中继承 `DIPEstimatorBase`
2. 实现估算逻辑
3. 集成到融合策略中
4. 训练和验证新模型

### 添加新的安全检查

1. 在 `real_time_controller.py` 的 `SafetyMonitor` 中添加检查逻辑
2. 更新配置文件中的安全参数
3. 添加相应的测试用例

## 📝 开发规范

### 代码风格
- 遵循 PEP 8 代码风格
- 使用类型提示 (Type Hints)
- 添加详细的文档字符串
- 保持函数和类的单一职责

### 测试要求
- 新功能必须包含单元测试
- 测试覆盖率应 >80%
- 性能关键路径需要基准测试
- 错误处理路径需要验证

### 文档要求
- 更新相关的 Markdown 文档
- 添加配置参数说明
- 提供使用示例
- 更新 API 文档

## 🔍 调试和故障排除

### 日志文件位置
- `logs/system.log`: 系统主日志
- `logs/performance.log`: 性能监控日志
- `logs/daemon.log`: 守护进程日志

### 常用调试命令
```bash
# 查看实时日志
tail -f logs/system.log

# 搜索错误信息
grep "ERROR" logs/system.log

# 查看性能统计
python main.py
>>> stats

# 运行诊断测试
python main.py --test
```

### 配置验证
```bash
# 验证配置文件语法
python -c "import yaml; yaml.safe_load(open('config.yaml'))"

# 检查传感器连接
python -c "from src.sensor_interface import FiberOpticSensor; ..."
```

这个项目结构设计确保了代码的模块化、可维护性和可扩展性，同时提供了完整的开发、测试和部署支持。
