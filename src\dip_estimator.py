"""
DIP关节估算器模块
结合生物力学模型和机器学习方法估算缺失的DIP关节角度
"""

import os
import time
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
import pickle
from abc import ABC, abstractmethod

from .utils import ConfigManager, Logger


@dataclass
class DIPEstimation:
    """DIP关节估算结果"""
    dip_angles: np.ndarray      # 4个DIP关节角度 (弧度)
    confidence: np.ndarray      # 置信度 (0-1)
    method_used: str            # 使用的估算方法
    timestamp: float            # 时间戳


class DIPEstimatorBase(ABC):
    """DIP估算器抽象基类"""
    
    @abstractmethod
    def estimate(self, sensor_data: np.ndarray, known_joints: np.ndarray) -> DIPEstimation:
        """估算DIP关节角度"""
        pass
    
    @abstractmethod
    def get_confidence(self, estimation: DIPEstimation) -> float:
        """获取估算置信度"""
        pass


class BiomechanicalModel(DIPEstimatorBase):
    """生物力学模型DIP估算器"""
    
    def __init__(self, config: ConfigManager, logger: Logger):
        """
        初始化生物力学模型
        
        Args:
            config: 配置管理器
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        
        # 生物力学参数
        self.dip_pip_ratios = config.get('dip_estimator.biomechanical.dip_pip_ratios', {
            'index': 0.67,
            'middle': 0.65,
            'ring': 0.63,
            'pinky': 0.60
        })
        
        self.mcp_influence_factor = config.get('dip_estimator.biomechanical.mcp_influence_factor', 0.1)
        self.nonlinear_correction_factor = config.get('dip_estimator.biomechanical.nonlinear_correction_factor', 0.05)
        
        # 手指名称映射
        self.finger_names = ['index', 'middle', 'ring', 'pinky']
        
        # 关节索引映射 (基于16DOF输出)
        self.joint_indices = {
            'thumb_cmc_flex': 0, 'thumb_cmc_abd': 1, 'thumb_mcp': 2, 'thumb_ip': 3,
            'index_mcp': 4, 'index_pip': 5, 'index_dip': 6,
            'middle_mcp': 7, 'middle_pip': 8, 'middle_dip': 9,
            'ring_mcp': 10, 'ring_pip': 11, 'ring_dip': 12,
            'pinky_mcp': 13, 'pinky_pip': 14, 'pinky_dip': 15
        }
    
    def estimate(self, sensor_data: np.ndarray, known_joints: np.ndarray) -> DIPEstimation:
        """
        使用生物力学模型估算DIP关节
        
        Args:
            sensor_data: 12DOF传感器数据
            known_joints: 已知关节角度 (12DOF对应的关节)
            
        Returns:
            DIP关节估算结果
        """
        dip_angles = np.zeros(4)  # 4个DIP关节
        confidence = np.ones(4) * 0.8  # 生物力学模型基础置信度
        
        try:
            for i, finger in enumerate(self.finger_names):
                # 获取对应的PIP和MCP关节角度
                pip_angle = self._get_pip_angle(finger, known_joints)
                mcp_angle = self._get_mcp_angle(finger, known_joints)
                
                # 基础DIP估算
                base_estimate = self.dip_pip_ratios[finger] * pip_angle
                
                # MCP影响修正
                mcp_influence = self.mcp_influence_factor * mcp_angle
                
                # 非线性修正（处理极端姿态）
                nonlinear_correction = self.nonlinear_correction_factor * np.sin(2 * pip_angle)
                
                # 生物力学约束
                biomechanical_constraint = self._apply_biomechanical_constraints(
                    pip_angle, mcp_angle, finger
                )
                
                # 最终估算
                dip_angles[i] = (base_estimate + mcp_influence + 
                               nonlinear_correction + biomechanical_constraint)
                
                # 置信度调整
                confidence[i] = self._calculate_confidence(pip_angle, mcp_angle, finger)
            
            # 应用关节限制
            dip_angles = self._apply_joint_limits(dip_angles)
            
        except Exception as e:
            self.logger.error(f"生物力学模型估算失败: {e}")
            confidence.fill(0.0)
        
        return DIPEstimation(
            dip_angles=dip_angles,
            confidence=confidence,
            method_used="biomechanical",
            timestamp=time.time()
        )
    
    def _get_pip_angle(self, finger: str, known_joints: np.ndarray) -> float:
        """获取PIP关节角度"""
        # 从12DOF传感器数据中提取PIP角度
        pip_indices = {
            'index': 1,    # 食指PIP
            'middle': 3,   # 中指PIP  
            'ring': 5,     # 无名指PIP
            'pinky': 7     # 小指PIP
        }
        return known_joints[pip_indices[finger]]
    
    def _get_mcp_angle(self, finger: str, known_joints: np.ndarray) -> float:
        """获取MCP关节角度"""
        # 从12DOF传感器数据中提取MCP角度
        mcp_indices = {
            'index': 0,    # 食指MCP
            'middle': 2,   # 中指MCP
            'ring': 4,     # 无名指MCP
            'pinky': 6     # 小指MCP
        }
        return known_joints[mcp_indices[finger]]
    
    def _apply_biomechanical_constraints(self, pip_angle: float, mcp_angle: float, 
                                       finger: str) -> float:
        """应用生物力学约束"""
        constraint = 0.0
        
        # DIP不应超过PIP的80%
        max_dip_ratio = 0.8
        if pip_angle > 0:
            constraint -= max(0, pip_angle * self.dip_pip_ratios[finger] - 
                            pip_angle * max_dip_ratio) * 0.5
        
        # 考虑手指间的协调性
        if finger in ['middle', 'ring']:
            # 中指和无名指通常协调运动
            coordination_factor = 0.02
            constraint += coordination_factor * np.sin(mcp_angle)
        
        return constraint
    
    def _calculate_confidence(self, pip_angle: float, mcp_angle: float, finger: str) -> float:
        """计算估算置信度"""
        base_confidence = 0.8
        
        # 角度范围内置信度更高
        if 0 <= pip_angle <= np.pi/2:  # 0-90度
            confidence = base_confidence
        else:
            confidence = base_confidence * 0.7
        
        # 极端角度降低置信度
        if abs(pip_angle) > np.pi * 0.8:  # 超过144度
            confidence *= 0.5
        
        if abs(mcp_angle) > np.pi * 0.7:  # 超过126度
            confidence *= 0.8
        
        return np.clip(confidence, 0.1, 1.0)
    
    def _apply_joint_limits(self, dip_angles: np.ndarray) -> np.ndarray:
        """应用DIP关节限制"""
        # DIP关节限制: 0-90度
        min_angle = 0.0
        max_angle = np.pi / 2
        
        return np.clip(dip_angles, min_angle, max_angle)
    
    def get_confidence(self, estimation: DIPEstimation) -> float:
        """获取整体置信度"""
        return np.mean(estimation.confidence)


class MLModel(DIPEstimatorBase):
    """机器学习DIP估算器"""
    
    def __init__(self, config: ConfigManager, logger: Logger):
        """
        初始化机器学习模型
        
        Args:
            config: 配置管理器
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        
        # 模型配置
        self.model_path = config.get('dip_estimator.ml_model.model_path', 'models/dip_estimator.pth')
        self.input_dim = config.get('dip_estimator.ml_model.input_dim', 12)
        self.output_dim = config.get('dip_estimator.ml_model.output_dim', 4)
        
        # 创建和加载模型
        self.model = self._create_model()
        self.model_loaded = self._load_model()
        
        # 数据标准化参数
        self.scaler_params = self._load_scaler_params()
        
        # 设备配置
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)
        self.model.eval()
    
    def _create_model(self) -> nn.Module:
        """创建神经网络模型"""
        hidden_dims = self.config.get('dip_estimator.ml_model.hidden_dims', [64, 32, 16])
        dropout_rate = self.config.get('dip_estimator.ml_model.dropout_rate', 0.1)
        
        layers = []
        
        # 输入层
        layers.append(nn.Linear(self.input_dim, hidden_dims[0]))
        layers.append(nn.ReLU())
        layers.append(nn.Dropout(dropout_rate))
        
        # 隐藏层
        for i in range(len(hidden_dims) - 1):
            layers.append(nn.Linear(hidden_dims[i], hidden_dims[i + 1]))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout_rate))
        
        # 输出层
        layers.append(nn.Linear(hidden_dims[-1], self.output_dim))
        layers.append(nn.Tanh())  # 限制输出范围
        
        return nn.Sequential(*layers)
    
    def _load_model(self) -> bool:
        """加载预训练模型"""
        if not os.path.exists(self.model_path):
            self.logger.warning(f"模型文件不存在: {self.model_path}")
            return False
        
        try:
            state_dict = torch.load(self.model_path, map_location=self.device)
            self.model.load_state_dict(state_dict)
            self.logger.info(f"成功加载模型: {self.model_path}")
            return True
        except Exception as e:
            self.logger.error(f"加载模型失败: {e}")
            return False
    
    def _load_scaler_params(self) -> Dict[str, np.ndarray]:
        """加载数据标准化参数"""
        scaler_path = self.model_path.replace('.pth', '_scaler.pkl')
        
        if os.path.exists(scaler_path):
            try:
                with open(scaler_path, 'rb') as f:
                    return pickle.load(f)
            except Exception as e:
                self.logger.warning(f"加载标准化参数失败: {e}")
        
        # 默认参数（无标准化）
        return {
            'mean': np.zeros(self.input_dim),
            'std': np.ones(self.input_dim)
        }
    
    def estimate(self, sensor_data: np.ndarray, known_joints: np.ndarray) -> DIPEstimation:
        """
        使用机器学习模型估算DIP关节
        
        Args:
            sensor_data: 12DOF传感器数据
            known_joints: 已知关节角度
            
        Returns:
            DIP关节估算结果
        """
        if not self.model_loaded:
            # 如果模型未加载，返回零值
            return DIPEstimation(
                dip_angles=np.zeros(4),
                confidence=np.zeros(4),
                method_used="ml_model_failed",
                timestamp=time.time()
            )
        
        try:
            # 数据预处理
            input_data = self._preprocess_input(sensor_data)
            
            # 模型推理
            with torch.no_grad():
                input_tensor = torch.FloatTensor(input_data).unsqueeze(0).to(self.device)
                output = self.model(input_tensor)
                dip_angles = output.cpu().numpy().flatten()
            
            # 后处理
            dip_angles = self._postprocess_output(dip_angles)
            
            # 计算置信度
            confidence = self._calculate_ml_confidence(input_data, dip_angles)
            
            return DIPEstimation(
                dip_angles=dip_angles,
                confidence=confidence,
                method_used="ml_model",
                timestamp=time.time()
            )
            
        except Exception as e:
            self.logger.error(f"机器学习模型估算失败: {e}")
            return DIPEstimation(
                dip_angles=np.zeros(4),
                confidence=np.zeros(4),
                method_used="ml_model_error",
                timestamp=time.time()
            )
    
    def _preprocess_input(self, sensor_data: np.ndarray) -> np.ndarray:
        """预处理输入数据"""
        # 数据标准化
        normalized_data = (sensor_data - self.scaler_params['mean']) / self.scaler_params['std']
        
        # 确保数据在合理范围内
        normalized_data = np.clip(normalized_data, -5, 5)
        
        return normalized_data
    
    def _postprocess_output(self, output: np.ndarray) -> np.ndarray:
        """后处理输出数据"""
        # 将tanh输出(-1,1)映射到关节角度范围(0, π/2)
        dip_angles = (output + 1) * np.pi / 4  # 映射到(0, π/2)
        
        # 应用关节限制
        dip_angles = np.clip(dip_angles, 0, np.pi / 2)
        
        return dip_angles
    
    def _calculate_ml_confidence(self, input_data: np.ndarray, output: np.ndarray) -> np.ndarray:
        """计算机器学习模型置信度"""
        base_confidence = 0.9 if self.model_loaded else 0.0
        
        # 基于输入数据质量调整置信度
        input_quality = 1.0 - np.mean(np.abs(input_data)) / 5.0  # 假设标准化后数据在[-5,5]范围
        input_quality = np.clip(input_quality, 0.1, 1.0)
        
        # 基于输出合理性调整置信度
        output_quality = 1.0
        if np.any(output < 0) or np.any(output > np.pi/2):
            output_quality *= 0.5
        
        final_confidence = base_confidence * input_quality * output_quality
        
        return np.full(4, final_confidence)
    
    def get_confidence(self, estimation: DIPEstimation) -> float:
        """获取整体置信度"""
        return np.mean(estimation.confidence)


class DIPEstimator:
    """DIP关节估算器主类，融合多种估算方法"""
    
    def __init__(self, config: ConfigManager, logger: Logger):
        """
        初始化DIP估算器
        
        Args:
            config: 配置管理器
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        
        # 创建子估算器
        self.biomechanical_model = BiomechanicalModel(config, logger)
        self.ml_model = MLModel(config, logger)
        
        # 融合策略配置
        self.biomechanical_weight = config.get('dip_estimator.fusion.biomechanical_weight', 0.3)
        self.ml_weight = config.get('dip_estimator.fusion.ml_weight', 0.7)
        self.confidence_threshold = config.get('dip_estimator.fusion.confidence_threshold', 0.8)
        
        # 历史估算结果（用于时间平滑）
        self.previous_estimation: Optional[DIPEstimation] = None
        self.temporal_smoothing_factor = 0.8
    
    def estimate_dip_joints(self, sensor_data: np.ndarray, known_joints: np.ndarray) -> DIPEstimation:
        """
        估算DIP关节角度
        
        Args:
            sensor_data: 12DOF传感器数据
            known_joints: 已知关节角度
            
        Returns:
            融合后的DIP关节估算结果
        """
        # 生物力学模型估算
        bio_estimation = self.biomechanical_model.estimate(sensor_data, known_joints)
        
        # 机器学习模型估算
        ml_estimation = self.ml_model.estimate(sensor_data, known_joints)
        
        # 融合估算结果
        fused_estimation = self._fuse_estimations(bio_estimation, ml_estimation)
        
        # 时间平滑
        if self.previous_estimation is not None:
            fused_estimation = self._apply_temporal_smoothing(fused_estimation)
        
        self.previous_estimation = fused_estimation
        
        return fused_estimation
    
    def _fuse_estimations(self, bio_est: DIPEstimation, ml_est: DIPEstimation) -> DIPEstimation:
        """
        融合多个估算结果
        
        Args:
            bio_est: 生物力学估算结果
            ml_est: 机器学习估算结果
            
        Returns:
            融合后的估算结果
        """
        # 计算权重（基于置信度）
        bio_confidence = self.biomechanical_model.get_confidence(bio_est)
        ml_confidence = self.ml_model.get_confidence(ml_est)
        
        # 自适应权重调整
        if ml_confidence < self.confidence_threshold:
            # ML模型置信度低时，增加生物力学模型权重
            effective_bio_weight = 0.8
            effective_ml_weight = 0.2
        elif bio_confidence < self.confidence_threshold:
            # 生物力学模型置信度低时，增加ML模型权重
            effective_bio_weight = 0.2
            effective_ml_weight = 0.8
        else:
            # 都有较高置信度时，使用配置的权重
            effective_bio_weight = self.biomechanical_weight
            effective_ml_weight = self.ml_weight
        
        # 归一化权重
        total_weight = effective_bio_weight + effective_ml_weight
        effective_bio_weight /= total_weight
        effective_ml_weight /= total_weight
        
        # 融合角度
        fused_angles = (effective_bio_weight * bio_est.dip_angles + 
                       effective_ml_weight * ml_est.dip_angles)
        
        # 融合置信度
        fused_confidence = (effective_bio_weight * bio_est.confidence + 
                          effective_ml_weight * ml_est.confidence)
        
        return DIPEstimation(
            dip_angles=fused_angles,
            confidence=fused_confidence,
            method_used="fused",
            timestamp=time.time()
        )
    
    def _apply_temporal_smoothing(self, current_estimation: DIPEstimation) -> DIPEstimation:
        """
        应用时间平滑
        
        Args:
            current_estimation: 当前估算结果
            
        Returns:
            平滑后的估算结果
        """
        if self.previous_estimation is None:
            return current_estimation
        
        # 平滑角度
        smoothed_angles = (self.temporal_smoothing_factor * current_estimation.dip_angles + 
                          (1 - self.temporal_smoothing_factor) * self.previous_estimation.dip_angles)
        
        # 平滑置信度
        smoothed_confidence = (self.temporal_smoothing_factor * current_estimation.confidence + 
                             (1 - self.temporal_smoothing_factor) * self.previous_estimation.confidence)
        
        return DIPEstimation(
            dip_angles=smoothed_angles,
            confidence=smoothed_confidence,
            method_used=current_estimation.method_used + "_smoothed",
            timestamp=current_estimation.timestamp
        )
    
    def get_estimation_quality(self, estimation: DIPEstimation) -> Dict[str, float]:
        """
        获取估算质量指标
        
        Args:
            estimation: DIP估算结果
            
        Returns:
            质量指标字典
        """
        return {
            'overall_confidence': np.mean(estimation.confidence),
            'min_confidence': np.min(estimation.confidence),
            'max_confidence': np.max(estimation.confidence),
            'confidence_std': np.std(estimation.confidence),
            'angle_range': np.max(estimation.dip_angles) - np.min(estimation.dip_angles),
            'method_used': estimation.method_used
        }
