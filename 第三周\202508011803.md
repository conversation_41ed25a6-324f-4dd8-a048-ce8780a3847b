吴恩达机器学习第十一天

使用矩阵的方式重写提供的方法
```python
### START CODE HERE ###
    # 计算线性组合，添加数值裁剪
    z = np.dot(X, w) + b
    z = np.clip(z, -500, 500)  # 防止溢出
    
    # 计算sigmoid，确保数值稳定
    f = sigmoid(z)
    f = np.clip(f, 1e-15, 1-1e-15)  # 防止log(0)

    loss =  -y * np.log(f) - (1.0 - y) * np.log(1.0 - f)
    
    return np.sum(loss) / m
        
    ### END CODE HERE ### 
```

```python
 ### START CODE HERE ### 
    err = 0.
    
    z = np.dot(X, w) + b        # 线性组合
    f_wb = sigmoid(z)           # sigmoid变换
    
    # 步骤2: 计算误差
    err = f_wb - y              # 预测误差
    
    # 步骤3: 计算梯度
    dj_dw = (1/m) * np.dot(X.T, err)  # 权重梯度
    dj_db = (1/m) * np.sum(err)       # 偏置梯度
    ### END CODE HERE ###
```

```python
 ### START CODE HERE ### 
    # Loop over each example

    p = sigmoid(np.dot(X, w) + b) >= 0.5
        
    ### END CODE HERE ### 
```

```Python

 ### START CODE HERE ###
    reg_cost = np.sum(w**2)

    ### END CODE HERE ### 
    
    # Add the regularization cost to get the total cost
    total_cost = cost_without_reg + (lambda_/(2 * m)) * reg_cost
```

```python
 ### START CODE HERE ###     
    dj_dw += np.dot(w.T, lambda_) / m 
        
    ### END CODE HERE ###      
```