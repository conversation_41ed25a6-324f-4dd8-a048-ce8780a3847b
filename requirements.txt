# 12DOF到16DOF手部运动映射系统依赖包

# 核心计算库
numpy>=1.21.0
scipy>=1.7.0
nlopt>=2.7.0

# 机器学习框架
torch>=1.12.0
torchvision>=0.13.0
scikit-learn>=1.1.0

# 数据处理
pandas>=1.4.0
h5py>=3.7.0

# 配置管理
PyYAML>=6.0
configargparse>=1.5.0

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.10.0

# 实时系统
threading-timer>=1.0.0
psutil>=5.9.0

# 机器人学库
pinocchio>=2.6.0

# 通信和接口
pyserial>=3.5
socket-io>=0.5.0

# 日志和监控
loguru>=0.6.0
tqdm>=4.64.0

# 测试框架
pytest>=7.1.0
pytest-cov>=3.0.0
pytest-mock>=3.8.0

# 代码质量
black>=22.0.0
flake8>=5.0.0
mypy>=0.971

# 性能分析
line-profiler>=3.5.0
memory-profiler>=0.60.0

# 数学优化
cvxpy>=1.2.0
casadi>=3.5.0

# 信号处理
filterpy>=1.4.0
