"""
工具函数和配置管理模块
提供配置管理、日志记录、性能监控等通用功能
"""

import os
import time
import yaml
import logging
import numpy as np
import threading
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from pathlib import Path
import psutil
from collections import deque


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    computation_time: float
    accuracy: float
    error_count: int
    timestamp: float
    memory_usage: float
    cpu_usage: float


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件未找到: {self.config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持嵌套键（如 'sensor.port'）
        
        Args:
            key: 配置键，支持点分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self, path: Optional[str] = None) -> None:
        """
        保存配置到文件
        
        Args:
            path: 保存路径，默认为原配置文件路径
        """
        save_path = path or self.config_path
        with open(save_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)


class Logger:
    """日志记录器"""
    
    def __init__(self, name: str, config: ConfigManager):
        """
        初始化日志记录器
        
        Args:
            name: 日志器名称
            config: 配置管理器
        """
        self.name = name
        self.config = config
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(self.name)
        
        # 设置日志级别
        log_level = self.config.get('system.log_level', 'INFO')
        logger.setLevel(getattr(logging, log_level))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器
        log_file = self.config.get('system.log_file')
        if log_file:
            # 确保日志目录存在
            log_dir = os.path.dirname(log_file)
            if log_dir:
                os.makedirs(log_dir, exist_ok=True)
            
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def debug(self, message: str) -> None:
        """记录调试信息"""
        self.logger.debug(message)
    
    def info(self, message: str) -> None:
        """记录信息"""
        self.logger.info(message)
    
    def warning(self, message: str) -> None:
        """记录警告"""
        self.logger.warning(message)
    
    def error(self, message: str) -> None:
        """记录错误"""
        self.logger.error(message)
    
    def critical(self, message: str) -> None:
        """记录严重错误"""
        self.logger.critical(message)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, config: ConfigManager, logger: Logger):
        """
        初始化性能监控器
        
        Args:
            config: 配置管理器
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        self.enabled = config.get('performance_monitor.enable', True)
        
        if self.enabled:
            self.window_size = config.get('performance_monitor.metrics_window_size', 1000)
            self.log_interval = config.get('performance_monitor.log_interval', 10.0)
            
            # 性能指标存储
            self.metrics_history: deque = deque(maxlen=self.window_size)
            self.start_time = time.time()
            self.last_log_time = time.time()
            
            # 阈值配置
            self.thresholds = {
                'max_computation_time': config.get('performance_monitor.thresholds.max_computation_time', 0.015),
                'min_accuracy': config.get('performance_monitor.thresholds.min_accuracy', 0.95),
                'max_error_rate': config.get('performance_monitor.thresholds.max_error_rate', 0.05)
            }
            
            # 启动监控线程
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
    
    def record_metrics(self, computation_time: float, accuracy: float = 1.0, 
                      error_occurred: bool = False) -> None:
        """
        记录性能指标
        
        Args:
            computation_time: 计算时间
            accuracy: 精度
            error_occurred: 是否发生错误
        """
        if not self.enabled:
            return
        
        # 获取系统资源使用情况
        process = psutil.Process()
        memory_usage = process.memory_percent()
        cpu_usage = process.cpu_percent()
        
        # 创建性能指标
        metrics = PerformanceMetrics(
            computation_time=computation_time,
            accuracy=accuracy,
            error_count=1 if error_occurred else 0,
            timestamp=time.time(),
            memory_usage=memory_usage,
            cpu_usage=cpu_usage
        )
        
        self.metrics_history.append(metrics)
        
        # 检查阈值
        self._check_thresholds(metrics)
    
    def _check_thresholds(self, metrics: PerformanceMetrics) -> None:
        """检查性能阈值"""
        if metrics.computation_time > self.thresholds['max_computation_time']:
            self.logger.warning(
                f"计算时间超过阈值: {metrics.computation_time:.4f}s > "
                f"{self.thresholds['max_computation_time']:.4f}s"
            )
        
        if metrics.accuracy < self.thresholds['min_accuracy']:
            self.logger.warning(
                f"精度低于阈值: {metrics.accuracy:.4f} < "
                f"{self.thresholds['min_accuracy']:.4f}"
            )
    
    def _monitoring_loop(self) -> None:
        """监控循环"""
        while True:
            time.sleep(self.log_interval)
            
            if len(self.metrics_history) > 0:
                self._log_performance_summary()
    
    def _log_performance_summary(self) -> None:
        """记录性能摘要"""
        if not self.metrics_history:
            return
        
        # 计算统计信息
        computation_times = [m.computation_time for m in self.metrics_history]
        accuracies = [m.accuracy for m in self.metrics_history]
        error_counts = [m.error_count for m in self.metrics_history]
        
        avg_computation_time = np.mean(computation_times)
        max_computation_time = np.max(computation_times)
        avg_accuracy = np.mean(accuracies)
        error_rate = np.sum(error_counts) / len(error_counts)
        
        # 记录摘要
        self.logger.info(
            f"性能摘要 - 平均计算时间: {avg_computation_time:.4f}s, "
            f"最大计算时间: {max_computation_time:.4f}s, "
            f"平均精度: {avg_accuracy:.4f}, "
            f"错误率: {error_rate:.4f}"
        )
    
    def get_performance_report(self) -> Dict[str, Any]:
        """
        获取性能报告
        
        Returns:
            性能报告字典
        """
        if not self.enabled or not self.metrics_history:
            return {}
        
        computation_times = [m.computation_time for m in self.metrics_history]
        accuracies = [m.accuracy for m in self.metrics_history]
        error_counts = [m.error_count for m in self.metrics_history]
        memory_usages = [m.memory_usage for m in self.metrics_history]
        cpu_usages = [m.cpu_usage for m in self.metrics_history]
        
        uptime = time.time() - self.start_time
        
        return {
            'uptime_seconds': uptime,
            'total_samples': len(self.metrics_history),
            'computation_time': {
                'mean': np.mean(computation_times),
                'std': np.std(computation_times),
                'min': np.min(computation_times),
                'max': np.max(computation_times),
                'p95': np.percentile(computation_times, 95)
            },
            'accuracy': {
                'mean': np.mean(accuracies),
                'std': np.std(accuracies),
                'min': np.min(accuracies)
            },
            'error_rate': np.sum(error_counts) / len(error_counts),
            'resource_usage': {
                'memory_percent': {
                    'mean': np.mean(memory_usages),
                    'max': np.max(memory_usages)
                },
                'cpu_percent': {
                    'mean': np.mean(cpu_usages),
                    'max': np.max(cpu_usages)
                }
            }
        }


def create_directories(config: ConfigManager) -> None:
    """
    创建必要的目录结构
    
    Args:
        config: 配置管理器
    """
    directories = [
        'logs',
        'data/recordings',
        'models',
        'plots',
        'tests/data'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


def validate_config(config: ConfigManager) -> bool:
    """
    验证配置文件的有效性
    
    Args:
        config: 配置管理器
        
    Returns:
        配置是否有效
    """
    required_keys = [
        'system.name',
        'sensor.interface_type',
        'robot_hand.dof_count',
        'real_time_controller.target_frequency'
    ]
    
    for key in required_keys:
        if config.get(key) is None:
            return False
    
    # 验证DOF数量
    if config.get('robot_hand.dof_count') != 16:
        return False
    
    # 验证关节名称数量
    joint_names = config.get('robot_hand.joint_names', [])
    if len(joint_names) != 16:
        return False
    
    return True


def degrees_to_radians(angles_deg: np.ndarray) -> np.ndarray:
    """
    角度转弧度
    
    Args:
        angles_deg: 角度数组
        
    Returns:
        弧度数组
    """
    return np.deg2rad(angles_deg)


def radians_to_degrees(angles_rad: np.ndarray) -> np.ndarray:
    """
    弧度转角度
    
    Args:
        angles_rad: 弧度数组
        
    Returns:
        角度数组
    """
    return np.rad2deg(angles_rad)


def apply_joint_limits(angles: np.ndarray, limits: List[tuple]) -> np.ndarray:
    """
    应用关节限制
    
    Args:
        angles: 关节角度数组（弧度）
        limits: 关节限制列表，每个元素为(min, max)元组（度）
        
    Returns:
        限制后的关节角度数组
    """
    limited_angles = angles.copy()
    
    for i, (min_deg, max_deg) in enumerate(limits):
        min_rad = np.deg2rad(min_deg)
        max_rad = np.deg2rad(max_deg)
        limited_angles[i] = np.clip(limited_angles[i], min_rad, max_rad)
    
    return limited_angles


def smooth_trajectory(current: np.ndarray, previous: np.ndarray, 
                     alpha: float = 0.8) -> np.ndarray:
    """
    轨迹平滑
    
    Args:
        current: 当前值
        previous: 前一个值
        alpha: 平滑系数 (0-1)
        
    Returns:
        平滑后的值
    """
    if previous is None:
        return current
    
    return alpha * current + (1 - alpha) * previous
