#!/bin/bash

# 12DOF到16DOF手部运动映射系统部署脚本
# Hand Motion Mapping System Deployment Script

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统要求
check_system_requirements() {
    log_info "检查系统要求..."
    
    # 检查Python版本
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        log_info "Python版本: $PYTHON_VERSION"
        
        # 检查Python版本是否>=3.8
        if python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
            log_success "Python版本检查通过"
        else
            log_error "Python版本必须>=3.8"
            exit 1
        fi
    else
        log_error "未找到Python3"
        exit 1
    fi
    
    # 检查pip
    if command -v pip3 &> /dev/null; then
        log_success "pip3已安装"
    else
        log_error "未找到pip3"
        exit 1
    fi
    
    # 检查内存
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    if [ "$MEMORY_GB" -ge 4 ]; then
        log_success "内存检查通过: ${MEMORY_GB}GB"
    else
        log_warning "内存不足4GB，可能影响性能: ${MEMORY_GB}GB"
    fi
    
    # 检查磁盘空间
    DISK_SPACE=$(df -h . | awk 'NR==2{print $4}' | sed 's/G//')
    if [ "${DISK_SPACE%.*}" -ge 2 ]; then
        log_success "磁盘空间检查通过: ${DISK_SPACE}G可用"
    else
        log_warning "磁盘空间不足2GB: ${DISK_SPACE}G可用"
    fi
}

# 创建虚拟环境
create_virtual_environment() {
    log_info "创建Python虚拟环境..."
    
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        log_success "虚拟环境创建成功"
    else
        log_info "虚拟环境已存在"
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    log_info "虚拟环境已激活"
    
    # 升级pip
    pip install --upgrade pip
    log_success "pip已升级到最新版本"
}

# 安装依赖
install_dependencies() {
    log_info "安装Python依赖包..."
    
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        log_success "依赖包安装完成"
    else
        log_error "未找到requirements.txt文件"
        exit 1
    fi
    
    # 验证关键包安装
    log_info "验证关键包安装..."
    python3 -c "import numpy, scipy, torch, nlopt" 2>/dev/null && log_success "关键包验证通过" || {
        log_error "关键包验证失败"
        exit 1
    }
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    directories=(
        "logs"
        "data/recordings"
        "models"
        "plots"
        "tests/data"
        "config/backup"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done
    
    log_success "目录结构创建完成"
}

# 配置系统
configure_system() {
    log_info "配置系统..."
    
    # 检查配置文件
    if [ ! -f "config.yaml" ]; then
        log_warning "未找到config.yaml，将创建默认配置"
        # 这里可以复制默认配置模板
        # cp config.yaml.template config.yaml
    else
        log_success "配置文件已存在"
    fi
    
    # 设置权限
    chmod +x main.py
    log_info "设置执行权限"
    
    # 检查串口设备权限（Linux）
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if [ -e "/dev/ttyUSB0" ]; then
            if [ -r "/dev/ttyUSB0" ] && [ -w "/dev/ttyUSB0" ]; then
                log_success "串口设备权限正常"
            else
                log_warning "串口设备权限不足，可能需要运行: sudo chmod 666 /dev/ttyUSB0"
            fi
        else
            log_info "未检测到串口设备 /dev/ttyUSB0"
        fi
    fi
}

# 运行测试
run_tests() {
    log_info "运行系统测试..."
    
    # 运行基本测试
    if python3 main.py --test; then
        log_success "基本测试通过"
    else
        log_error "基本测试失败"
        return 1
    fi
    
    # 运行单元测试
    if [ -f "tests/test_system.py" ]; then
        log_info "运行单元测试..."
        if python3 -m pytest tests/ -v; then
            log_success "单元测试通过"
        else
            log_warning "单元测试失败，但不影响部署"
        fi
    fi
    
    return 0
}

# 创建启动脚本
create_startup_scripts() {
    log_info "创建启动脚本..."
    
    # 创建启动脚本
    cat > start_system.sh << 'EOF'
#!/bin/bash
# 手部运动映射系统启动脚本

cd "$(dirname "$0")"

# 激活虚拟环境
if [ -d "venv" ]; then
    source venv/bin/activate
fi

# 启动系统
python3 main.py "$@"
EOF
    
    chmod +x start_system.sh
    log_success "启动脚本创建完成: start_system.sh"
    
    # 创建守护进程启动脚本
    cat > start_daemon.sh << 'EOF'
#!/bin/bash
# 手部运动映射系统守护进程启动脚本

cd "$(dirname "$0")"

# 激活虚拟环境
if [ -d "venv" ]; then
    source venv/bin/activate
fi

# 启动守护进程
nohup python3 main.py --daemon > logs/daemon.log 2>&1 &
echo $! > logs/daemon.pid

echo "守护进程已启动，PID: $(cat logs/daemon.pid)"
echo "日志文件: logs/daemon.log"
EOF
    
    chmod +x start_daemon.sh
    log_success "守护进程启动脚本创建完成: start_daemon.sh"
    
    # 创建停止脚本
    cat > stop_daemon.sh << 'EOF'
#!/bin/bash
# 停止守护进程脚本

if [ -f "logs/daemon.pid" ]; then
    PID=$(cat logs/daemon.pid)
    if kill -0 $PID 2>/dev/null; then
        kill $PID
        echo "守护进程已停止 (PID: $PID)"
        rm logs/daemon.pid
    else
        echo "守护进程未运行"
        rm logs/daemon.pid
    fi
else
    echo "未找到PID文件"
fi
EOF
    
    chmod +x stop_daemon.sh
    log_success "停止脚本创建完成: stop_daemon.sh"
}

# 创建systemd服务（Linux）
create_systemd_service() {
    if [[ "$OSTYPE" == "linux-gnu"* ]] && command -v systemctl &> /dev/null; then
        log_info "创建systemd服务..."
        
        SERVICE_FILE="/etc/systemd/system/hand-motion-mapping.service"
        CURRENT_DIR=$(pwd)
        CURRENT_USER=$(whoami)
        
        cat > hand-motion-mapping.service << EOF
[Unit]
Description=Hand Motion Mapping System
After=network.target

[Service]
Type=simple
User=$CURRENT_USER
WorkingDirectory=$CURRENT_DIR
ExecStart=$CURRENT_DIR/venv/bin/python $CURRENT_DIR/main.py --daemon
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
        
        log_info "systemd服务文件已创建: hand-motion-mapping.service"
        log_info "要安装服务，请运行:"
        log_info "  sudo cp hand-motion-mapping.service $SERVICE_FILE"
        log_info "  sudo systemctl daemon-reload"
        log_info "  sudo systemctl enable hand-motion-mapping"
        log_info "  sudo systemctl start hand-motion-mapping"
    fi
}

# 生成部署报告
generate_deployment_report() {
    log_info "生成部署报告..."
    
    REPORT_FILE="deployment_report.txt"
    
    cat > $REPORT_FILE << EOF
=== 手部运动映射系统部署报告 ===

部署时间: $(date)
系统信息: $(uname -a)
Python版本: $(python3 --version)
工作目录: $(pwd)

已安装的Python包:
$(pip list)

目录结构:
$(find . -type d -name ".*" -prune -o -type d -print | head -20)

配置文件:
$(ls -la *.yaml *.yml 2>/dev/null || echo "无配置文件")

启动脚本:
$(ls -la *.sh)

部署状态: 成功
EOF
    
    log_success "部署报告已生成: $REPORT_FILE"
}

# 显示使用说明
show_usage_instructions() {
    log_info "部署完成！使用说明:"
    echo ""
    echo "1. 交互模式启动:"
    echo "   ./start_system.sh"
    echo ""
    echo "2. 守护进程模式启动:"
    echo "   ./start_daemon.sh"
    echo ""
    echo "3. 停止守护进程:"
    echo "   ./stop_daemon.sh"
    echo ""
    echo "4. 查看日志:"
    echo "   tail -f logs/system.log"
    echo ""
    echo "5. 运行测试:"
    echo "   python3 main.py --test"
    echo ""
    echo "6. 查看帮助:"
    echo "   python3 main.py --help"
    echo ""
    log_success "系统已准备就绪！"
}

# 主函数
main() {
    log_info "开始部署12DOF到16DOF手部运动映射系统..."
    echo ""
    
    # 检查是否在正确的目录
    if [ ! -f "main.py" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行部署步骤
    check_system_requirements
    create_virtual_environment
    install_dependencies
    create_directories
    configure_system
    
    # 运行测试（可选）
    if [ "${1:-}" != "--skip-tests" ]; then
        run_tests || log_warning "测试失败，但继续部署"
    fi
    
    create_startup_scripts
    create_systemd_service
    generate_deployment_report
    
    echo ""
    log_success "部署完成！"
    show_usage_instructions
}

# 处理命令行参数
case "${1:-}" in
    --help|-h)
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  --skip-tests    跳过测试步骤"
        echo "  --help, -h      显示此帮助信息"
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
