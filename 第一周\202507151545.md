今日学习进度
1. 构想人体3D模型渲染的生成方案

### python基础知识 ：
1. 编码
对于单个字符的编码，Python提供了 `ord()` 函数获取字符的整数表示，`chr()` 函数把编码转换为对应的字符：
```python
>>> ord('A')
65
>>> ord('中')
20013
>>> chr(66)
'B'
>>> chr(25991)
'文'

# 如果知道字符的整数编码，还可以用十六进制这么写`str`(意义不大)
>>> '\u4e2d\u6587'
'中文'
```

2. 字节数和字符数的区别
`len()` 函数计算的是 `str` 的字符数，如果换成 `bytes`，`len()` 函数就计算字节数：

```python
>>> len(b'ABC')
3
>>> len(b'\xe4\xb8\xad\xe6\x96\x87')
6
>>> len('中文'.encode('utf-8'))
6
```

可见，1个中文字符经过UTF-8编码后通常会占用3个字节，而1个英文字符只占用1个字节。
3. 格式化输出
% 、format、以及f-string