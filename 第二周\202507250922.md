吴恩达机器学习第七天

图像绘制问题
```python
# 使用plt绘制多个图像发现报错，究其原因是创建的ax数组结构不同，上面创建的是(4,) ，而下面的是(2, 2)，所以可以采用展平数组，或者2D遍历的方法
# fig,ax=plt.subplots(1, 4, figsize=(12, 3), sharey=True)
# for i in range(len(ax)):
#     ax[i].scatter(X_train[:,i],y_train)
#     ax[i].set_xlabel(X_features[i])
# ax[0].set_ylabel("Price (1000's)")
# plt.show()

# 数组展平
fig, ax = plt.subplots(2, 2, sharey=True)
ax = ax.flatten()
for i in range(len(ax)):
    ax[i].scatter(X_train[:, i], y_train)
    ax[i].set_xlabel(X_features[i])
ax[0].set_ylabel("Price (1000's)")
plt.show()

```

函数作为参数调用
```python
a = np.zeros(10)

# YY作为ZZ的函数参数，再由YY调用XX
def XX(x):
  x += 1
  return x
  
def YY(y):
  return XX(y)

def ZZ(x, YY):
  return YY(x)

print(ZZ(a, YY))
```
在跑二元一次方程Y = x^2 + 1 的时候发现学习率很低或者迭代次数需要很多才能收敛
```python
# 主要是特征方程的范围过大
X = X.reshape(-1, 1)  #X should be a 2-D Matrix
model_w,model_b = run_gradient_descent_feng(X, y, iterations=1000000, alpha = 5e-5)
plt.scatter(x, y, marker='x', c='r', label="Actual Value"); plt.title("Added x**2 feature")
plt.plot(x, np.dot(X,model_w) + model_b, label="Predicted Value"); plt.xlabel("x"); plt.ylabel("y"); plt.legend(); plt.show()

# 这里多加一步标准化的操作
X_norm = zscore_normalize_features(X)

model_w,model_b = run_gradient_descent_feng(X_norm, y, iterations=200, alpha = 1e-1)
# 其速度的提升是显著的，在1e-1的学习率下，200次就达到了收敛
```

之后就是导入sklearn库进行相关操作，读api调用，没什么好讲的
