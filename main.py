#!/usr/bin/env python3
"""
12DOF到16DOF手部运动映射系统主程序
Hand Motion Mapping System Main Entry Point
"""

import os
import sys
import argparse
import time
import signal
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils import ConfigManager, Logger, create_directories, validate_config
from src.sensor_interface import FiberOpticSensor
from src.motion_mapper import MotionMapper
from src.real_time_controller import RealTimeController, SystemStatus, ControllerState
import numpy as np


class HandMotionMappingSystem:
    """手部运动映射系统主类"""
    
    def __init__(self, config_path: str):
        """
        初始化系统
        
        Args:
            config_path: 配置文件路径
        """
        # 加载配置
        self.config = ConfigManager(config_path)
        
        # 验证配置
        if not validate_config(self.config):
            raise ValueError("配置文件验证失败")
        
        # 创建必要目录
        create_directories(self.config)
        
        # 初始化日志
        self.logger = Logger("HandMotionMapping", self.config)
        
        # 初始化组件
        self.sensor_interface = None
        self.motion_mapper = None
        self.controller = None
        
        # 系统状态
        self.running = False
        self.start_time = 0.0
        
        # 性能统计
        self.total_frames = 0
        self.successful_frames = 0
        
        self.logger.info("手部运动映射系统初始化完成")
    
    def initialize_components(self) -> bool:
        """
        初始化系统组件
        
        Returns:
            初始化是否成功
        """
        try:
            self.logger.info("初始化系统组件...")
            
            # 初始化传感器接口
            self.sensor_interface = FiberOpticSensor(self.config, self.logger)
            
            # 初始化运动映射器
            self.motion_mapper = MotionMapper(self.config, self.logger)
            
            # 初始化实时控制器
            self.controller = RealTimeController(
                self.config, self.logger, 
                self.sensor_interface, self.motion_mapper
            )
            
            # 设置回调函数
            self.controller.set_output_callback(self._output_callback)
            self.controller.set_status_callback(self._status_callback)
            
            self.logger.info("系统组件初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"组件初始化失败: {e}")
            return False
    
    def start(self) -> bool:
        """
        启动系统
        
        Returns:
            启动是否成功
        """
        if self.running:
            self.logger.warning("系统已在运行中")
            return False
        
        try:
            self.logger.info("启动手部运动映射系统...")
            
            # 初始化组件
            if not self.initialize_components():
                return False
            
            # 启动控制器
            if not self.controller.start():
                self.logger.error("控制器启动失败")
                return False
            
            self.running = True
            self.start_time = time.time()
            
            self.logger.info("系统启动成功")
            return True
            
        except Exception as e:
            self.logger.error(f"系统启动失败: {e}")
            return False
    
    def stop(self) -> None:
        """停止系统"""
        if not self.running:
            return
        
        self.logger.info("停止手部运动映射系统...")
        
        try:
            # 停止控制器
            if self.controller:
                self.controller.stop()
            
            # 生成最终报告
            self._generate_final_report()
            
            self.running = False
            self.logger.info("系统已停止")
            
        except Exception as e:
            self.logger.error(f"系统停止异常: {e}")
    
    def run_interactive_mode(self) -> None:
        """运行交互模式"""
        self.logger.info("进入交互模式")
        
        print("\n=== 手部运动映射系统交互模式 ===")
        print("可用命令:")
        print("  start  - 启动系统")
        print("  stop   - 停止系统")
        print("  pause  - 暂停系统")
        print("  resume - 恢复系统")
        print("  status - 显示系统状态")
        print("  stats  - 显示性能统计")
        print("  help   - 显示帮助")
        print("  quit   - 退出程序")
        print()
        
        while True:
            try:
                command = input(">>> ").strip().lower()
                
                if command == "start":
                    if self.start():
                        print("系统启动成功")
                    else:
                        print("系统启动失败")
                
                elif command == "stop":
                    self.stop()
                    print("系统已停止")
                
                elif command == "pause":
                    if self.controller:
                        self.controller.pause()
                        print("系统已暂停")
                    else:
                        print("系统未运行")
                
                elif command == "resume":
                    if self.controller:
                        self.controller.resume()
                        print("系统已恢复")
                    else:
                        print("系统未运行")
                
                elif command == "status":
                    self._print_system_status()
                
                elif command == "stats":
                    self._print_performance_stats()
                
                elif command == "help":
                    print("可用命令: start, stop, pause, resume, status, stats, help, quit")
                
                elif command == "quit":
                    if self.running:
                        self.stop()
                    break
                
                else:
                    print(f"未知命令: {command}")
                    
            except KeyboardInterrupt:
                print("\n检测到Ctrl+C，正在退出...")
                if self.running:
                    self.stop()
                break
            except EOFError:
                break
            except Exception as e:
                print(f"命令执行错误: {e}")
    
    def run_daemon_mode(self) -> None:
        """运行守护进程模式"""
        self.logger.info("进入守护进程模式")
        
        # 设置信号处理
        def signal_handler(signum, frame):
            self.logger.info(f"接收到信号 {signum}，正在停止系统...")
            self.stop()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 启动系统
        if not self.start():
            self.logger.error("系统启动失败")
            sys.exit(1)
        
        # 保持运行
        try:
            while self.running:
                time.sleep(1.0)
                
                # 检查系统状态
                if self.controller:
                    status = self.controller.get_status()
                    if status.controller_state == ControllerState.ERROR:
                        self.logger.error("检测到系统错误，停止运行")
                        break
                        
        except KeyboardInterrupt:
            self.logger.info("接收到中断信号")
        finally:
            self.stop()
    
    def _output_callback(self, robot_joints: np.ndarray) -> None:
        """
        输出回调函数
        
        Args:
            robot_joints: 16DOF机器人关节角度
        """
        self.total_frames += 1
        
        # 这里可以添加实际的机器人控制代码
        # 例如：发送关节角度到机器人控制器
        
        # 简单的控制台输出（调试用）
        if self.config.get('system.debug_mode', False):
            joint_degrees = np.rad2deg(robot_joints)
            self.logger.debug(f"输出关节角度: {joint_degrees}")
    
    def _status_callback(self, status: SystemStatus) -> None:
        """
        状态回调函数
        
        Args:
            status: 系统状态
        """
        # 记录状态变化
        if hasattr(self, '_last_state'):
            if self._last_state != status.controller_state:
                self.logger.info(f"系统状态变化: {self._last_state} -> {status.controller_state}")
        
        self._last_state = status.controller_state
        
        # 更新成功帧计数
        if status.controller_state == ControllerState.RUNNING:
            self.successful_frames += 1
    
    def _print_system_status(self) -> None:
        """打印系统状态"""
        if not self.controller:
            print("系统未初始化")
            return
        
        status = self.controller.get_status()
        
        print(f"\n=== 系统状态 ===")
        print(f"控制器状态: {status.controller_state.value}")
        print(f"传感器连接: {'是' if status.sensor_connected else '否'}")
        print(f"运行时间: {status.uptime:.1f}秒")
        print(f"错误计数: {status.error_count}")
        
        if status.performance_metrics:
            metrics = status.performance_metrics
            print(f"总样本数: {metrics.get('total_samples', 0)}")
            if 'computation_time' in metrics:
                comp_time = metrics['computation_time']
                print(f"平均计算时间: {comp_time.get('mean', 0):.4f}秒")
                print(f"最大计算时间: {comp_time.get('max', 0):.4f}秒")
        print()
    
    def _print_performance_stats(self) -> None:
        """打印性能统计"""
        if not self.motion_mapper:
            print("运动映射器未初始化")
            return
        
        stats = self.motion_mapper.get_performance_stats()
        
        print(f"\n=== 性能统计 ===")
        print(f"总映射次数: {stats.get('total_mappings', 0)}")
        print(f"成功映射次数: {stats.get('successful_mappings', 0)}")
        print(f"成功率: {stats.get('success_rate', 0):.2%}")
        print(f"平均计算时间: {stats.get('average_computation_time', 0):.4f}秒")
        print(f"平均映射质量: {stats.get('average_quality', 0):.3f}")
        
        if 'optimizer_stats' in stats:
            opt_stats = stats['optimizer_stats']
            print(f"优化器成功率: {opt_stats.get('success_rate', 0):.2%}")
            print(f"平均迭代次数: {opt_stats.get('average_iterations', 0):.1f}")
        print()
    
    def _generate_final_report(self) -> None:
        """生成最终报告"""
        if self.start_time == 0:
            return
        
        runtime = time.time() - self.start_time
        
        self.logger.info("=== 系统运行报告 ===")
        self.logger.info(f"总运行时间: {runtime:.1f}秒")
        self.logger.info(f"总处理帧数: {self.total_frames}")
        self.logger.info(f"成功处理帧数: {self.successful_frames}")
        
        if self.total_frames > 0:
            success_rate = self.successful_frames / self.total_frames
            avg_fps = self.total_frames / runtime
            self.logger.info(f"成功率: {success_rate:.2%}")
            self.logger.info(f"平均帧率: {avg_fps:.1f} FPS")
        
        # 保存详细统计到文件
        if self.motion_mapper:
            stats = self.motion_mapper.get_performance_stats()
            stats_file = "logs/final_performance_stats.txt"
            try:
                with open(stats_file, 'w') as f:
                    f.write("=== 最终性能统计 ===\n")
                    for key, value in stats.items():
                        f.write(f"{key}: {value}\n")
                self.logger.info(f"详细统计已保存到: {stats_file}")
            except Exception as e:
                self.logger.warning(f"保存统计文件失败: {e}")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="12DOF到16DOF手部运动映射系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                          # 交互模式
  python main.py --daemon                 # 守护进程模式
  python main.py --config custom.yaml    # 使用自定义配置
  python main.py --test                   # 测试模式
        """
    )
    
    parser.add_argument(
        '--config', '-c',
        default='config.yaml',
        help='配置文件路径 (默认: config.yaml)'
    )
    
    parser.add_argument(
        '--daemon', '-d',
        action='store_true',
        help='以守护进程模式运行'
    )
    
    parser.add_argument(
        '--test', '-t',
        action='store_true',
        help='运行系统测试'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出模式'
    )
    
    return parser.parse_args()


def run_system_test():
    """运行系统测试"""
    print("运行系统测试...")
    
    # 这里可以添加系统测试代码
    # 例如：测试各个组件的初始化、基本功能等
    
    try:
        # 测试配置加载
        config = ConfigManager('config.yaml')
        print("✓ 配置加载测试通过")
        
        # 测试组件初始化
        logger = Logger("Test", config)
        print("✓ 日志系统测试通过")
        
        # 可以添加更多测试...
        
        print("所有测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


def main():
    """主函数"""
    args = parse_arguments()
    
    # 运行测试模式
    if args.test:
        success = run_system_test()
        sys.exit(0 if success else 1)
    
    # 检查配置文件
    if not os.path.exists(args.config):
        print(f"错误: 配置文件不存在: {args.config}")
        sys.exit(1)
    
    try:
        # 创建系统实例
        system = HandMotionMappingSystem(args.config)
        
        # 根据模式运行
        if args.daemon:
            system.run_daemon_mode()
        else:
            system.run_interactive_mode()
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"系统运行异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
