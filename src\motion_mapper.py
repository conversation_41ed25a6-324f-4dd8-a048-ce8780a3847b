"""
运动映射核心模块
实现12DOF到16DOF的分层映射架构
"""

import time
import numpy as np
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass

from .utils import Config<PERSON>anager, Logger, apply_joint_limits, smooth_trajectory
from .sensor_interface import SensorData
from .dip_estimator import DIPEstimator, DIPEstimation
from .slsqp_optimizer import SLSQPOptimizer, OptimizationResult


@dataclass
class MappingResult:
    """映射结果数据类"""
    robot_joints: np.ndarray        # 16DOF机器人关节角度
    dip_estimation: DIPEstimation   # DIP关节估算结果
    optimization_result: OptimizationResult  # 优化结果
    mapping_quality: float          # 映射质量 (0-1)
    computation_time: float         # 计算时间
    timestamp: float               # 时间戳


class HumanHandModel:
    """人手模型类"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化人手模型
        
        Args:
            config: 配置管理器
        """
        self.config = config
        
        # 人手几何参数
        self.hand_geometry = {
            'palm_length': 0.18,
            'palm_width': 0.08,
            'finger_lengths': {
                'thumb': [0.05, 0.035, 0.03],
                'index': [0.055, 0.03, 0.025],
                'middle': [0.06, 0.035, 0.028],
                'ring': [0.058, 0.032, 0.026],
                'pinky': [0.05, 0.025, 0.02]
            }
        }
    
    def compute_keypoints_from_sensor(self, sensor_data: SensorData) -> Dict[str, np.ndarray]:
        """
        从传感器数据计算人手关键点位置
        
        Args:
            sensor_data: 传感器数据
            
        Returns:
            关键点位置字典
        """
        joint_angles = sensor_data.joint_angles
        keypoints = {}
        
        # 手腕位置（参考原点）
        keypoints['wrist'] = np.array([0.0, 0.0, 0.0])
        
        # 计算各手指指尖位置
        keypoints['thumb_tip'] = self._compute_human_thumb_tip(joint_angles)
        keypoints['index_tip'] = self._compute_human_finger_tip('index', joint_angles)
        keypoints['middle_tip'] = self._compute_human_finger_tip('middle', joint_angles)
        keypoints['ring_tip'] = self._compute_human_finger_tip('ring', joint_angles)
        keypoints['pinky_tip'] = self._compute_human_finger_tip('pinky', joint_angles)
        
        return keypoints
    
    def _compute_human_thumb_tip(self, joint_angles: np.ndarray) -> np.ndarray:
        """计算人手拇指指尖位置"""
        # 拇指关节角度 (从12DOF传感器数据中提取)
        # 假设传感器数据格式: [index_mcp, index_pip, middle_mcp, middle_pip, 
        #                     ring_mcp, ring_pip, pinky_mcp, pinky_pip,
        #                     thumb_cmc_flex, thumb_cmc_abd, thumb_mcp, thumb_ip]
        
        thumb_cmc_flex = joint_angles[8]
        thumb_cmc_abd = joint_angles[9]
        thumb_mcp = joint_angles[10]
        thumb_ip = joint_angles[11]
        
        # 拇指基础位置
        base_pos = np.array([0.03, -0.04, 0.0])
        
        # 计算各段长度和变换
        lengths = self.hand_geometry['finger_lengths']['thumb']
        
        # 简化的运动学计算
        # CMC关节变换
        cmc_x = lengths[0] * np.cos(thumb_cmc_flex) * np.cos(thumb_cmc_abd)
        cmc_y = lengths[0] * np.cos(thumb_cmc_flex) * np.sin(thumb_cmc_abd)
        cmc_z = lengths[0] * np.sin(thumb_cmc_flex)
        
        # MCP关节变换
        mcp_x = lengths[1] * np.cos(thumb_mcp)
        mcp_z = lengths[1] * np.sin(thumb_mcp)
        
        # IP关节变换
        ip_x = lengths[2] * np.cos(thumb_ip)
        ip_z = lengths[2] * np.sin(thumb_ip)
        
        # 最终位置
        tip_position = base_pos + np.array([
            cmc_x + mcp_x + ip_x,
            cmc_y,
            cmc_z + mcp_z + ip_z
        ])
        
        return tip_position
    
    def _compute_human_finger_tip(self, finger: str, joint_angles: np.ndarray) -> np.ndarray:
        """计算人手手指指尖位置"""
        # 获取关节角度索引
        joint_indices = {
            'index': {'mcp': 0, 'pip': 1},
            'middle': {'mcp': 2, 'pip': 3},
            'ring': {'mcp': 4, 'pip': 5},
            'pinky': {'mcp': 6, 'pip': 7}
        }
        
        indices = joint_indices[finger]
        mcp_angle = joint_angles[indices['mcp']]
        pip_angle = joint_angles[indices['pip']]
        
        # 估算DIP角度（简化）
        dip_ratios = {'index': 0.67, 'middle': 0.65, 'ring': 0.63, 'pinky': 0.60}
        dip_angle = dip_ratios[finger] * pip_angle
        
        # 手指基础位置
        base_positions = {
            'index': np.array([0.0, 0.025, 0.0]),
            'middle': np.array([0.0, 0.0, 0.0]),
            'ring': np.array([0.0, -0.025, 0.0]),
            'pinky': np.array([0.0, -0.05, 0.0])
        }
        base_pos = base_positions[finger]
        
        # 手指段长度
        lengths = self.hand_geometry['finger_lengths'][finger]
        
        # 简化的运动学计算
        # 第一段（掌骨）
        segment1_x = lengths[0] * np.cos(mcp_angle)
        segment1_z = lengths[0] * np.sin(mcp_angle)
        
        # 第二段（近节指骨）
        segment2_x = lengths[1] * np.cos(mcp_angle + pip_angle)
        segment2_z = lengths[1] * np.sin(mcp_angle + pip_angle)
        
        # 第三段（远节指骨）
        segment3_x = lengths[2] * np.cos(mcp_angle + pip_angle + dip_angle)
        segment3_z = lengths[2] * np.sin(mcp_angle + pip_angle + dip_angle)
        
        # 最终位置
        tip_position = base_pos + np.array([
            segment1_x + segment2_x + segment3_x,
            0.0,
            segment1_z + segment2_z + segment3_z
        ])
        
        return tip_position


class MotionMapper:
    """运动映射核心类"""
    
    def __init__(self, config: ConfigManager, logger: Logger):
        """
        初始化运动映射器
        
        Args:
            config: 配置管理器
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        
        # 初始化子模块
        self.dip_estimator = DIPEstimator(config, logger)
        self.slsqp_optimizer = SLSQPOptimizer(config, logger)
        self.human_hand_model = HumanHandModel(config)
        
        # 关节限制
        self.joint_limits = self._load_joint_limits()
        
        # 历史数据
        self.previous_result: Optional[MappingResult] = None
        self.mapping_history = []
        self.max_history_length = 10
        
        # 性能监控
        self.performance_stats = {
            'total_mappings': 0,
            'successful_mappings': 0,
            'average_computation_time': 0.0,
            'average_quality': 0.0
        }
        
        # 平滑参数
        self.temporal_smoothing_alpha = 0.8
        self.enable_smoothing = True
    
    def _load_joint_limits(self) -> List[Tuple[float, float]]:
        """加载关节限制"""
        joint_limits_config = self.config.get('robot_hand.joint_limits', {})
        joint_names = self.config.get('robot_hand.joint_names', [])
        
        limits = []
        for joint_name in joint_names:
            if joint_name in joint_limits_config:
                min_deg, max_deg = joint_limits_config[joint_name]
                limits.append((np.deg2rad(min_deg), np.deg2rad(max_deg)))
            else:
                limits.append((-np.pi, np.pi))
        
        return limits
    
    def map_motion(self, sensor_data: SensorData) -> MappingResult:
        """
        执行12DOF到16DOF运动映射
        
        Args:
            sensor_data: 传感器数据
            
        Returns:
            映射结果
        """
        start_time = time.time()
        
        try:
            # 第一层：直接映射已知关节
            known_joints = self._direct_mapping(sensor_data)
            
            # 第二层：估算DIP关节
            dip_estimation = self.dip_estimator.estimate_dip_joints(
                sensor_data.joint_angles, known_joints
            )
            
            # 第三层：SLSQP全局优化
            initial_guess = self._combine_joints(known_joints, dip_estimation.dip_angles)
            
            # 计算人手关键点
            human_keypoints = self.human_hand_model.compute_keypoints_from_sensor(sensor_data)
            
            # 执行优化
            optimization_result = self.slsqp_optimizer.optimize(
                initial_guess, human_keypoints, sensor_data.joint_angles
            )
            
            # 后处理
            final_joints = self._postprocess_joints(optimization_result.optimized_joints)
            
            # 计算映射质量
            mapping_quality = self._compute_mapping_quality(
                sensor_data, dip_estimation, optimization_result
            )
            
            computation_time = time.time() - start_time
            
            # 创建结果
            result = MappingResult(
                robot_joints=final_joints,
                dip_estimation=dip_estimation,
                optimization_result=optimization_result,
                mapping_quality=mapping_quality,
                computation_time=computation_time,
                timestamp=time.time()
            )
            
            # 更新历史和统计
            self._update_history(result)
            self._update_stats(result)
            
            self.previous_result = result
            
            return result
            
        except Exception as e:
            self.logger.error(f"运动映射失败: {e}")
            
            # 返回安全的默认结果
            computation_time = time.time() - start_time
            return self._create_fallback_result(sensor_data, computation_time)
    
    def _direct_mapping(self, sensor_data: SensorData) -> np.ndarray:
        """
        直接映射已知关节
        
        Args:
            sensor_data: 传感器数据
            
        Returns:
            已知关节角度 (12DOF)
        """
        # 传感器数据格式: [index_mcp, index_pip, middle_mcp, middle_pip,
        #                 ring_mcp, ring_pip, pinky_mcp, pinky_pip,
        #                 thumb_cmc_flex, thumb_cmc_abd, thumb_mcp, thumb_ip]
        
        # 映射到16DOF格式: [thumb_cmc_flex, thumb_cmc_abd, thumb_mcp, thumb_ip,
        #                  index_mcp, index_pip, index_dip,
        #                  middle_mcp, middle_pip, middle_dip,
        #                  ring_mcp, ring_pip, ring_dip,
        #                  pinky_mcp, pinky_pip, pinky_dip]
        
        sensor_angles = sensor_data.joint_angles
        known_joints = np.zeros(12)  # 不包括DIP关节的12个已知关节
        
        # 拇指关节
        known_joints[0] = sensor_angles[8]   # thumb_cmc_flex
        known_joints[1] = sensor_angles[9]   # thumb_cmc_abd
        known_joints[2] = sensor_angles[10]  # thumb_mcp
        known_joints[3] = sensor_angles[11]  # thumb_ip
        
        # 食指关节
        known_joints[4] = sensor_angles[0]   # index_mcp
        known_joints[5] = sensor_angles[1]   # index_pip
        
        # 中指关节
        known_joints[6] = sensor_angles[2]   # middle_mcp
        known_joints[7] = sensor_angles[3]   # middle_pip
        
        # 无名指关节
        known_joints[8] = sensor_angles[4]   # ring_mcp
        known_joints[9] = sensor_angles[5]   # ring_pip
        
        # 小指关节
        known_joints[10] = sensor_angles[6]  # pinky_mcp
        known_joints[11] = sensor_angles[7]  # pinky_pip
        
        return known_joints
    
    def _combine_joints(self, known_joints: np.ndarray, dip_angles: np.ndarray) -> np.ndarray:
        """
        组合已知关节和估算的DIP关节
        
        Args:
            known_joints: 已知关节角度 (12DOF)
            dip_angles: DIP关节角度 (4DOF)
            
        Returns:
            完整的16DOF关节角度
        """
        full_joints = np.zeros(16)
        
        # 拇指关节 (0-3)
        full_joints[0:4] = known_joints[0:4]
        
        # 食指关节 (4-6)
        full_joints[4] = known_joints[4]    # index_mcp
        full_joints[5] = known_joints[5]    # index_pip
        full_joints[6] = dip_angles[0]      # index_dip
        
        # 中指关节 (7-9)
        full_joints[7] = known_joints[6]    # middle_mcp
        full_joints[8] = known_joints[7]    # middle_pip
        full_joints[9] = dip_angles[1]      # middle_dip
        
        # 无名指关节 (10-12)
        full_joints[10] = known_joints[8]   # ring_mcp
        full_joints[11] = known_joints[9]   # ring_pip
        full_joints[12] = dip_angles[2]     # ring_dip
        
        # 小指关节 (13-15)
        full_joints[13] = known_joints[10]  # pinky_mcp
        full_joints[14] = known_joints[11]  # pinky_pip
        full_joints[15] = dip_angles[3]     # pinky_dip
        
        return full_joints
    
    def _postprocess_joints(self, joints: np.ndarray) -> np.ndarray:
        """
        后处理关节角度
        
        Args:
            joints: 原始关节角度
            
        Returns:
            处理后的关节角度
        """
        processed_joints = joints.copy()
        
        # 应用关节限制
        processed_joints = apply_joint_limits(processed_joints, 
                                            [(np.rad2deg(limit[0]), np.rad2deg(limit[1])) 
                                             for limit in self.joint_limits])
        
        # 时间平滑
        if (self.enable_smoothing and 
            self.previous_result is not None):
            processed_joints = smooth_trajectory(
                processed_joints,
                self.previous_result.robot_joints,
                self.temporal_smoothing_alpha
            )
        
        return processed_joints
    
    def _compute_mapping_quality(self, sensor_data: SensorData,
                               dip_estimation: DIPEstimation,
                               optimization_result: OptimizationResult) -> float:
        """
        计算映射质量
        
        Args:
            sensor_data: 传感器数据
            dip_estimation: DIP估算结果
            optimization_result: 优化结果
            
        Returns:
            映射质量 (0-1)
        """
        quality_factors = []
        
        # 1. 传感器数据质量
        quality_factors.append(sensor_data.quality)
        
        # 2. DIP估算置信度
        dip_confidence = np.mean(dip_estimation.confidence)
        quality_factors.append(dip_confidence)
        
        # 3. 优化成功率
        optimization_quality = 1.0 if optimization_result.success else 0.3
        quality_factors.append(optimization_quality)
        
        # 4. 计算时间惩罚
        max_computation_time = self.config.get('real_time_controller.max_computation_time', 0.01)
        time_penalty = min(1.0, max_computation_time / optimization_result.computation_time)
        quality_factors.append(time_penalty)
        
        # 5. 目标函数值（归一化）
        if optimization_result.objective_value < float('inf'):
            objective_quality = 1.0 / (1.0 + optimization_result.objective_value)
        else:
            objective_quality = 0.0
        quality_factors.append(objective_quality)
        
        # 计算加权平均质量
        weights = [0.3, 0.25, 0.2, 0.15, 0.1]
        overall_quality = np.average(quality_factors, weights=weights)
        
        return np.clip(overall_quality, 0.0, 1.0)
    
    def _update_history(self, result: MappingResult) -> None:
        """更新映射历史"""
        self.mapping_history.append(result)
        
        if len(self.mapping_history) > self.max_history_length:
            self.mapping_history.pop(0)
    
    def _update_stats(self, result: MappingResult) -> None:
        """更新性能统计"""
        self.performance_stats['total_mappings'] += 1
        
        if result.optimization_result.success:
            self.performance_stats['successful_mappings'] += 1
        
        # 更新平均值
        total = self.performance_stats['total_mappings']
        self.performance_stats['average_computation_time'] = (
            (self.performance_stats['average_computation_time'] * (total - 1) + 
             result.computation_time) / total
        )
        self.performance_stats['average_quality'] = (
            (self.performance_stats['average_quality'] * (total - 1) + 
             result.mapping_quality) / total
        )
    
    def _create_fallback_result(self, sensor_data: SensorData, 
                              computation_time: float) -> MappingResult:
        """创建回退结果"""
        # 使用上一次的结果或默认安全位置
        if self.previous_result is not None:
            fallback_joints = self.previous_result.robot_joints.copy()
        else:
            # 默认安全位置（所有关节为0）
            fallback_joints = np.zeros(16)
        
        # 创建默认DIP估算
        fallback_dip = DIPEstimation(
            dip_angles=np.zeros(4),
            confidence=np.zeros(4),
            method_used="fallback",
            timestamp=time.time()
        )
        
        # 创建默认优化结果
        fallback_optimization = OptimizationResult(
            optimized_joints=fallback_joints,
            objective_value=float('inf'),
            success=False,
            iterations=0,
            computation_time=computation_time,
            convergence_info="Fallback mode"
        )
        
        return MappingResult(
            robot_joints=fallback_joints,
            dip_estimation=fallback_dip,
            optimization_result=fallback_optimization,
            mapping_quality=0.0,
            computation_time=computation_time,
            timestamp=time.time()
        )
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = self.performance_stats.copy()
        
        if stats['total_mappings'] > 0:
            stats['success_rate'] = (stats['successful_mappings'] / 
                                   stats['total_mappings'])
        else:
            stats['success_rate'] = 0.0
        
        # 添加优化器统计
        stats['optimizer_stats'] = self.slsqp_optimizer.get_optimization_stats()
        
        return stats
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.performance_stats = {
            'total_mappings': 0,
            'successful_mappings': 0,
            'average_computation_time': 0.0,
            'average_quality': 0.0
        }
        self.slsqp_optimizer.reset_stats()
    
    def set_smoothing_enabled(self, enabled: bool) -> None:
        """设置时间平滑开关"""
        self.enable_smoothing = enabled
    
    def set_smoothing_alpha(self, alpha: float) -> None:
        """设置平滑系数"""
        self.temporal_smoothing_alpha = np.clip(alpha, 0.0, 1.0)
