"""
实时控制器模块
管理系统的实时运行，包括数据采集、处理和输出控制
"""

import time
import threading
import signal
import sys
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import queue
import numpy as np

from .utils import ConfigMana<PERSON>, Logger, PerformanceMonitor
from .sensor_interface import SensorInterface, SensorData
from .motion_mapper import MotionMapper, MappingResult


class ControllerState(Enum):
    """控制器状态枚举"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    PAUSING = "pausing"
    PAUSED = "paused"
    STOPPING = "stopping"
    ERROR = "error"


@dataclass
class ControlCommand:
    """控制命令数据类"""
    command_type: str           # 命令类型
    parameters: Dict[str, Any]  # 命令参数
    timestamp: float           # 时间戳


@dataclass
class SystemStatus:
    """系统状态数据类"""
    controller_state: ControllerState
    sensor_connected: bool
    last_sensor_data_time: float
    last_mapping_time: float
    error_count: int
    uptime: float
    performance_metrics: Dict[str, Any]


class SafetyMonitor:
    """安全监控器"""
    
    def __init__(self, config: ConfigManager, logger: Logger):
        """
        初始化安全监控器
        
        Args:
            config: 配置管理器
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        
        # 安全配置
        self.enable_emergency_stop = config.get('real_time_controller.safety.enable_emergency_stop', True)
        self.max_joint_velocity = np.deg2rad(config.get('real_time_controller.safety.max_joint_velocity', 180))
        self.max_joint_acceleration = np.deg2rad(config.get('real_time_controller.safety.max_joint_acceleration', 360))
        
        # 历史数据
        self.previous_joints: Optional[np.ndarray] = None
        self.previous_velocity: Optional[np.ndarray] = None
        self.previous_time: Optional[float] = None
        
        # 安全状态
        self.emergency_stop_triggered = False
        self.safety_violations = []
    
    def check_safety(self, joints: np.ndarray, timestamp: float) -> bool:
        """
        检查安全条件
        
        Args:
            joints: 关节角度
            timestamp: 时间戳
            
        Returns:
            是否安全
        """
        if not self.enable_emergency_stop:
            return True
        
        try:
            # 检查关节速度
            if not self._check_joint_velocity(joints, timestamp):
                return False
            
            # 检查关节加速度
            if not self._check_joint_acceleration(joints, timestamp):
                return False
            
            # 检查工作空间限制
            if not self._check_workspace_limits(joints):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"安全检查异常: {e}")
            return False
    
    def _check_joint_velocity(self, joints: np.ndarray, timestamp: float) -> bool:
        """检查关节速度"""
        if self.previous_joints is None or self.previous_time is None:
            self.previous_joints = joints.copy()
            self.previous_time = timestamp
            return True
        
        dt = timestamp - self.previous_time
        if dt <= 0:
            return True
        
        velocity = (joints - self.previous_joints) / dt
        max_velocity = np.max(np.abs(velocity))
        
        if max_velocity > self.max_joint_velocity:
            self.logger.warning(f"关节速度超限: {np.rad2deg(max_velocity):.1f}°/s > {np.rad2deg(self.max_joint_velocity):.1f}°/s")
            self.safety_violations.append(f"joint_velocity_exceeded_{timestamp}")
            return False
        
        self.previous_joints = joints.copy()
        self.previous_time = timestamp
        return True
    
    def _check_joint_acceleration(self, joints: np.ndarray, timestamp: float) -> bool:
        """检查关节加速度"""
        if self.previous_velocity is None:
            return True
        
        dt = timestamp - self.previous_time if self.previous_time else 0.01
        if dt <= 0:
            return True
        
        current_velocity = (joints - self.previous_joints) / dt if self.previous_joints is not None else np.zeros_like(joints)
        acceleration = (current_velocity - self.previous_velocity) / dt
        max_acceleration = np.max(np.abs(acceleration))
        
        if max_acceleration > self.max_joint_acceleration:
            self.logger.warning(f"关节加速度超限: {np.rad2deg(max_acceleration):.1f}°/s² > {np.rad2deg(self.max_joint_acceleration):.1f}°/s²")
            self.safety_violations.append(f"joint_acceleration_exceeded_{timestamp}")
            return False
        
        self.previous_velocity = current_velocity.copy()
        return True
    
    def _check_workspace_limits(self, joints: np.ndarray) -> bool:
        """检查工作空间限制"""
        # 简化的工作空间检查
        # 这里可以添加更复杂的工作空间约束检查
        return True
    
    def trigger_emergency_stop(self, reason: str) -> None:
        """触发紧急停止"""
        self.emergency_stop_triggered = True
        self.logger.critical(f"紧急停止触发: {reason}")
    
    def reset_emergency_stop(self) -> None:
        """重置紧急停止"""
        self.emergency_stop_triggered = False
        self.safety_violations.clear()
        self.logger.info("紧急停止已重置")


class RealTimeController:
    """实时控制器主类"""
    
    def __init__(self, config: ConfigManager, logger: Logger, 
                 sensor_interface: SensorInterface, motion_mapper: MotionMapper):
        """
        初始化实时控制器
        
        Args:
            config: 配置管理器
            logger: 日志记录器
            sensor_interface: 传感器接口
            motion_mapper: 运动映射器
        """
        self.config = config
        self.logger = logger
        self.sensor_interface = sensor_interface
        self.motion_mapper = motion_mapper
        
        # 控制参数
        self.target_frequency = config.get('real_time_controller.target_frequency', 50)
        self.target_period = 1.0 / self.target_frequency
        self.max_computation_time = config.get('real_time_controller.max_computation_time', 0.01)
        
        # 错误处理配置
        self.max_consecutive_errors = config.get('real_time_controller.error_handling.max_consecutive_errors', 5)
        self.error_recovery_timeout = config.get('real_time_controller.error_handling.error_recovery_timeout', 1.0)
        self.fallback_mode = config.get('real_time_controller.error_handling.fallback_mode', 'last_valid')
        
        # 状态管理
        self.state = ControllerState.STOPPED
        self.start_time = 0.0
        self.consecutive_errors = 0
        self.last_error_time = 0.0
        
        # 数据缓冲
        self.command_queue = queue.Queue(maxsize=100)
        self.result_queue = queue.Queue(maxsize=100)
        
        # 线程管理
        self.control_thread: Optional[threading.Thread] = None
        self.monitoring_thread: Optional[threading.Thread] = None
        self.running = False
        
        # 性能监控
        self.performance_monitor = PerformanceMonitor(config, logger)
        
        # 安全监控
        self.safety_monitor = SafetyMonitor(config, logger)
        
        # 回调函数
        self.output_callback: Optional[Callable[[np.ndarray], None]] = None
        self.status_callback: Optional[Callable[[SystemStatus], None]] = None
        
        # 最后有效结果
        self.last_valid_result: Optional[MappingResult] = None
        
        # 信号处理
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self) -> None:
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"接收到信号 {signum}，正在停止控制器...")
            self.stop()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def set_output_callback(self, callback: Callable[[np.ndarray], None]) -> None:
        """设置输出回调函数"""
        self.output_callback = callback
    
    def set_status_callback(self, callback: Callable[[SystemStatus], None]) -> None:
        """设置状态回调函数"""
        self.status_callback = callback
    
    def start(self) -> bool:
        """
        启动实时控制器
        
        Returns:
            启动是否成功
        """
        if self.state != ControllerState.STOPPED:
            self.logger.warning("控制器已在运行中")
            return False
        
        self.logger.info("启动实时控制器...")
        self.state = ControllerState.STARTING
        
        try:
            # 连接传感器
            if not self.sensor_interface.connect():
                self.logger.error("传感器连接失败")
                self.state = ControllerState.ERROR
                return False
            
            # 重置统计信息
            self.motion_mapper.reset_stats()
            self.consecutive_errors = 0
            self.start_time = time.time()
            
            # 启动控制线程
            self.running = True
            self.control_thread = threading.Thread(target=self._control_loop, daemon=True)
            self.control_thread.start()
            
            # 启动监控线程
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            
            self.state = ControllerState.RUNNING
            self.logger.info("实时控制器启动成功")
            return True
            
        except Exception as e:
            self.logger.error(f"启动控制器失败: {e}")
            self.state = ControllerState.ERROR
            return False
    
    def stop(self) -> None:
        """停止实时控制器"""
        if self.state == ControllerState.STOPPED:
            return
        
        self.logger.info("停止实时控制器...")
        self.state = ControllerState.STOPPING
        
        # 停止线程
        self.running = False
        
        # 等待线程结束
        if self.control_thread and self.control_thread.is_alive():
            self.control_thread.join(timeout=2.0)
        
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=1.0)
        
        # 断开传感器
        self.sensor_interface.disconnect()
        
        self.state = ControllerState.STOPPED
        self.logger.info("实时控制器已停止")
    
    def pause(self) -> None:
        """暂停控制器"""
        if self.state == ControllerState.RUNNING:
            self.state = ControllerState.PAUSING
            self.logger.info("控制器已暂停")
    
    def resume(self) -> None:
        """恢复控制器"""
        if self.state == ControllerState.PAUSED:
            self.state = ControllerState.RUNNING
            self.logger.info("控制器已恢复")
    
    def _control_loop(self) -> None:
        """主控制循环"""
        self.logger.info("控制循环启动")
        
        while self.running:
            loop_start_time = time.time()
            
            try:
                # 检查状态
                if self.state == ControllerState.PAUSING:
                    self.state = ControllerState.PAUSED
                    continue
                elif self.state == ControllerState.PAUSED:
                    time.sleep(0.01)
                    continue
                elif self.state != ControllerState.RUNNING:
                    break
                
                # 检查紧急停止
                if self.safety_monitor.emergency_stop_triggered:
                    self.logger.warning("紧急停止激活，跳过本次循环")
                    time.sleep(0.1)
                    continue
                
                # 读取传感器数据
                sensor_data = self.sensor_interface.read_data()
                if sensor_data is None or not sensor_data.is_valid:
                    self._handle_sensor_error()
                    continue
                
                # 执行运动映射
                mapping_result = self.motion_mapper.map_motion(sensor_data)
                
                # 安全检查
                if not self.safety_monitor.check_safety(mapping_result.robot_joints, mapping_result.timestamp):
                    self.logger.warning("安全检查失败，使用安全位置")
                    mapping_result = self._get_safe_position()
                
                # 输出控制信号
                if self.output_callback:
                    self.output_callback(mapping_result.robot_joints)
                
                # 记录性能指标
                computation_time = mapping_result.computation_time
                self.performance_monitor.record_metrics(
                    computation_time=computation_time,
                    accuracy=mapping_result.mapping_quality,
                    error_occurred=not mapping_result.optimization_result.success
                )
                
                # 更新最后有效结果
                if mapping_result.mapping_quality > 0.5:
                    self.last_valid_result = mapping_result
                
                # 重置错误计数
                self.consecutive_errors = 0
                
                # 频率控制
                self._maintain_frequency(loop_start_time)
                
            except Exception as e:
                self._handle_control_error(e)
        
        self.logger.info("控制循环结束")
    
    def _monitoring_loop(self) -> None:
        """监控循环"""
        while self.running:
            try:
                # 生成系统状态
                status = self._generate_system_status()
                
                # 调用状态回调
                if self.status_callback:
                    self.status_callback(status)
                
                # 检查系统健康状态
                self._check_system_health(status)
                
                time.sleep(1.0)  # 每秒检查一次
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(1.0)
    
    def _handle_sensor_error(self) -> None:
        """处理传感器错误"""
        self.consecutive_errors += 1
        self.last_error_time = time.time()
        
        if self.consecutive_errors >= self.max_consecutive_errors:
            self.logger.error(f"连续传感器错误达到{self.max_consecutive_errors}次，触发错误恢复")
            self._trigger_error_recovery()
        else:
            self.logger.warning(f"传感器错误 ({self.consecutive_errors}/{self.max_consecutive_errors})")
    
    def _handle_control_error(self, error: Exception) -> None:
        """处理控制错误"""
        self.consecutive_errors += 1
        self.last_error_time = time.time()
        self.logger.error(f"控制循环错误: {error}")
        
        if self.consecutive_errors >= self.max_consecutive_errors:
            self.logger.critical("连续错误过多，停止控制器")
            self.state = ControllerState.ERROR
            self.running = False
    
    def _trigger_error_recovery(self) -> None:
        """触发错误恢复"""
        self.logger.info("开始错误恢复...")
        
        # 等待恢复超时
        time.sleep(self.error_recovery_timeout)
        
        # 尝试重新连接传感器
        if not self.sensor_interface.is_connected():
            if self.sensor_interface.connect():
                self.logger.info("传感器重连成功")
                self.consecutive_errors = 0
            else:
                self.logger.error("传感器重连失败")
                self.state = ControllerState.ERROR
    
    def _get_safe_position(self) -> MappingResult:
        """获取安全位置"""
        if self.fallback_mode == "last_valid" and self.last_valid_result:
            return self.last_valid_result
        elif self.fallback_mode == "safe_position":
            # 返回预定义的安全位置
            safe_joints = np.zeros(16)  # 所有关节为0度
            return self._create_safe_mapping_result(safe_joints)
        else:
            # 停止模式
            self.safety_monitor.trigger_emergency_stop("安全检查失败")
            return self._create_safe_mapping_result(np.zeros(16))
    
    def _create_safe_mapping_result(self, joints: np.ndarray) -> MappingResult:
        """创建安全映射结果"""
        from .dip_estimator import DIPEstimation
        from .slsqp_optimizer import OptimizationResult
        
        safe_dip = DIPEstimation(
            dip_angles=np.zeros(4),
            confidence=np.ones(4),
            method_used="safe_position",
            timestamp=time.time()
        )
        
        safe_optimization = OptimizationResult(
            optimized_joints=joints,
            objective_value=0.0,
            success=True,
            iterations=0,
            computation_time=0.0,
            convergence_info="Safe position"
        )
        
        return MappingResult(
            robot_joints=joints,
            dip_estimation=safe_dip,
            optimization_result=safe_optimization,
            mapping_quality=1.0,
            computation_time=0.0,
            timestamp=time.time()
        )
    
    def _maintain_frequency(self, start_time: float) -> None:
        """维持控制频率"""
        elapsed_time = time.time() - start_time
        
        if elapsed_time > self.max_computation_time:
            self.logger.warning(f"计算时间超限: {elapsed_time:.4f}s > {self.max_computation_time:.4f}s")
        
        sleep_time = self.target_period - elapsed_time
        if sleep_time > 0:
            time.sleep(sleep_time)
    
    def _generate_system_status(self) -> SystemStatus:
        """生成系统状态"""
        current_time = time.time()
        uptime = current_time - self.start_time if self.start_time > 0 else 0
        
        return SystemStatus(
            controller_state=self.state,
            sensor_connected=self.sensor_interface.is_connected(),
            last_sensor_data_time=current_time,  # 简化实现
            last_mapping_time=current_time,      # 简化实现
            error_count=self.consecutive_errors,
            uptime=uptime,
            performance_metrics=self.performance_monitor.get_performance_report()
        )
    
    def _check_system_health(self, status: SystemStatus) -> None:
        """检查系统健康状态"""
        # 检查传感器连接
        if not status.sensor_connected:
            self.logger.warning("传感器连接丢失")
        
        # 检查错误率
        if status.error_count > self.max_consecutive_errors // 2:
            self.logger.warning(f"错误计数较高: {status.error_count}")
        
        # 检查性能指标
        perf_metrics = status.performance_metrics
        if perf_metrics and 'error_rate' in perf_metrics:
            if perf_metrics['error_rate'] > 0.1:  # 错误率超过10%
                self.logger.warning(f"系统错误率较高: {perf_metrics['error_rate']:.2%}")
    
    def get_status(self) -> SystemStatus:
        """获取当前系统状态"""
        return self._generate_system_status()
    
    def send_command(self, command: ControlCommand) -> bool:
        """
        发送控制命令
        
        Args:
            command: 控制命令
            
        Returns:
            命令是否成功发送
        """
        try:
            self.command_queue.put_nowait(command)
            return True
        except queue.Full:
            self.logger.warning("命令队列已满")
            return False
    
    def emergency_stop(self, reason: str = "用户触发") -> None:
        """紧急停止"""
        self.safety_monitor.trigger_emergency_stop(reason)
        self.logger.critical(f"紧急停止: {reason}")
    
    def reset_emergency_stop(self) -> None:
        """重置紧急停止"""
        self.safety_monitor.reset_emergency_stop()
        self.logger.info("紧急停止已重置")
