# 12DOF到16DOF手部运动映射系统配置文件

# 系统基本配置
system:
  name: "Hand Motion Mapping System"
  version: "1.0.0"
  debug_mode: false
  log_level: "INFO"
  log_file: "logs/system.log"

# 传感器配置
sensor:
  interface_type: "fiber_optic"  # 光纤传感器接口类型
  port: "/dev/ttyUSB0"          # 串口设备
  baudrate: 115200              # 波特率
  timeout: 0.01                 # 读取超时(秒)
  sampling_rate: 100            # 采样频率(Hz)
  data_format: "12dof_angles"   # 数据格式
  calibration_file: "config/sensor_calibration.yaml"
  
  # 数据预处理
  preprocessing:
    enable_filter: true
    filter_type: "butterworth"   # 滤波器类型
    filter_order: 2
    cutoff_frequency: 10.0       # 截止频率(Hz)
    enable_outlier_detection: true
    outlier_threshold: 3.0       # 异常值检测阈值(标准差倍数)

# 机器人手配置
robot_hand:
  dof_count: 16                 # 输出自由度数量
  joint_names:
    - "thumb_cmc_flex"          # 拇指腕掌关节屈伸
    - "thumb_cmc_abd"           # 拇指腕掌关节外展
    - "thumb_mcp"               # 拇指掌指关节
    - "thumb_ip"                # 拇指指间关节
    - "index_mcp"               # 食指掌指关节
    - "index_pip"               # 食指近指关节
    - "index_dip"               # 食指远指关节
    - "middle_mcp"              # 中指掌指关节
    - "middle_pip"              # 中指近指关节
    - "middle_dip"              # 中指远指关节
    - "ring_mcp"                # 无名指掌指关节
    - "ring_pip"                # 无名指近指关节
    - "ring_dip"                # 无名指远指关节
    - "pinky_mcp"               # 小指掌指关节
    - "pinky_pip"               # 小指近指关节
    - "pinky_dip"               # 小指远指关节
  
  # 关节限制(度)
  joint_limits:
    thumb_cmc_flex: [-30, 30]
    thumb_cmc_abd: [-30, 30]
    thumb_mcp: [0, 60]
    thumb_ip: [0, 90]
    index_mcp: [-10, 90]
    index_pip: [0, 110]
    index_dip: [0, 90]
    middle_mcp: [-10, 90]
    middle_pip: [0, 110]
    middle_dip: [0, 90]
    ring_mcp: [-10, 90]
    ring_pip: [0, 110]
    ring_dip: [0, 90]
    pinky_mcp: [-10, 90]
    pinky_pip: [0, 110]
    pinky_dip: [0, 90]

# DIP关节估算器配置
dip_estimator:
  # 生物力学模型参数
  biomechanical:
    enable: true
    dip_pip_ratios:
      index: 0.67
      middle: 0.65
      ring: 0.63
      pinky: 0.60
    mcp_influence_factor: 0.1
    nonlinear_correction_factor: 0.05
  
  # 机器学习模型配置
  ml_model:
    enable: true
    model_path: "models/dip_estimator.pth"
    input_dim: 12
    hidden_dims: [64, 32, 16]
    output_dim: 4  # 4个DIP关节
    dropout_rate: 0.1
    
    # 训练参数
    training:
      learning_rate: 0.001
      batch_size: 32
      epochs: 100
      validation_split: 0.2
      early_stopping_patience: 10
  
  # 融合策略
  fusion:
    biomechanical_weight: 0.3
    ml_weight: 0.7
    confidence_threshold: 0.8

# SLSQP优化器配置
slsqp_optimizer:
  # 优化参数
  ftol: 1.0e-6                  # 函数收敛容差
  xtol: 1.0e-6                  # 变量收敛容差
  maxiter: 100                  # 最大迭代次数
  finite_diff_rel_step: 1.0e-8 # 有限差分相对步长
  
  # 目标函数权重
  weights:
    keypoint_matching: 1.0      # 关键点匹配权重
    temporal_smooth: 0.1        # 时间平滑权重
    dip_constraint: 0.5         # DIP约束权重
    joint_limit: 10.0           # 关节限制权重
    biomechanical: 2.0          # 生物力学约束权重
    collision_avoidance: 5.0    # 碰撞避免权重
  
  # 关键向量定义
  keypoint_vectors:
    - ["wrist", "thumb_tip"]
    - ["wrist", "index_tip"]
    - ["wrist", "middle_tip"]
    - ["wrist", "ring_tip"]
    - ["wrist", "pinky_tip"]
    - ["thumb_tip", "index_tip"]
    - ["index_tip", "middle_tip"]
    - ["middle_tip", "ring_tip"]

# 实时控制器配置
real_time_controller:
  target_frequency: 50          # 目标控制频率(Hz)
  max_computation_time: 0.01    # 最大计算时间(秒)
  enable_performance_monitor: true
  
  # 错误处理
  error_handling:
    max_consecutive_errors: 5
    error_recovery_timeout: 1.0  # 错误恢复超时(秒)
    fallback_mode: "last_valid"  # 回退模式: last_valid, safe_position, stop
  
  # 安全配置
  safety:
    enable_emergency_stop: true
    max_joint_velocity: 180      # 最大关节速度(度/秒)
    max_joint_acceleration: 360  # 最大关节加速度(度/秒²)
    workspace_limits:
      enable: true
      safety_margin: 0.05        # 安全边界(米)

# 性能监控配置
performance_monitor:
  enable: true
  metrics_window_size: 1000     # 性能指标窗口大小
  log_interval: 10.0            # 日志记录间隔(秒)
  
  # 性能阈值
  thresholds:
    max_computation_time: 0.015  # 最大计算时间阈值(秒)
    min_accuracy: 0.95           # 最小精度阈值
    max_error_rate: 0.05         # 最大错误率阈值
  
  # 报告配置
  reporting:
    enable_periodic_reports: true
    report_interval: 300         # 报告间隔(秒)
    save_detailed_logs: true
    performance_log_file: "logs/performance.log"

# 数据记录配置
data_logging:
  enable: false
  output_directory: "data/recordings"
  file_format: "hdf5"           # 文件格式: hdf5, csv, json
  compression: true
  
  # 记录内容
  record_items:
    sensor_data: true
    estimated_dip: true
    optimized_joints: true
    performance_metrics: true
    timestamps: true

# 可视化配置
visualization:
  enable: false
  update_frequency: 10          # 可视化更新频率(Hz)
  plot_types:
    - "joint_angles"
    - "estimation_accuracy"
    - "computation_time"
  save_plots: false
  plot_directory: "plots"

# 测试配置
testing:
  enable_unit_tests: true
  test_data_directory: "tests/data"
  synthetic_data:
    enable: true
    noise_level: 0.01           # 噪声水平
    outlier_probability: 0.02   # 异常值概率
  
  # 性能基准
  benchmarks:
    target_accuracy: 0.95
    target_frequency: 50
    max_latency: 0.02
