# 12DOF到16DOF手部运动映射系统
# Hand Motion Mapping System from 12DOF to 16DOF

__version__ = "1.0.0"
__author__ = "Hand Motion Mapping Team"
__description__ = "Real-time hand motion mapping system using SLSQP optimization and machine learning"

from .sensor_interface import SensorInterface, FiberOpticSensor
from .dip_estimator import DIPEstimator, BiomechanicalModel, MLModel
from .slsqp_optimizer import SLSQPOptimizer
from .motion_mapper import MotionMapper
from .real_time_controller import RealTimeController
from .utils import ConfigManager, Logger, PerformanceMonitor

__all__ = [
    "SensorInterface",
    "FiberOpticSensor", 
    "DIPEstimator",
    "BiomechanicalModel",
    "MLModel",
    "SLSQPOptimizer",
    "MotionMapper",
    "RealTimeController",
    "ConfigManager",
    "Logger",
    "PerformanceMonitor"
]
