"""
传感器接口模块
处理12DOF光纤传感器信号输入，提供数据预处理和实时数据流功能
"""

import os
import time
import yaml
import serial
import numpy as np
import threading
from abc import ABC, abstractmethod
from typing import Optional, Callable, Dict, Any, List
from dataclasses import dataclass
from collections import deque
from scipy import signal
from filterpy.kalman import KalmanFilter

from .utils import ConfigManager, Logger


@dataclass
class SensorData:
    """传感器数据结构"""
    joint_angles: np.ndarray  # 12DOF关节角度 (弧度)
    timestamp: float          # 时间戳
    quality: float           # 数据质量指标 (0-1)
    is_valid: bool           # 数据是否有效


class SensorInterface(ABC):
    """传感器接口抽象基类"""
    
    @abstractmethod
    def connect(self) -> bool:
        """连接传感器"""
        pass
    
    @abstractmethod
    def disconnect(self) -> None:
        """断开传感器连接"""
        pass
    
    @abstractmethod
    def read_data(self) -> Optional[SensorData]:
        """读取传感器数据"""
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """检查连接状态"""
        pass


class DataPreprocessor:
    """数据预处理器"""
    
    def __init__(self, config: ConfigManager, logger: Logger):
        """
        初始化数据预处理器
        
        Args:
            config: 配置管理器
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        
        # 滤波器配置
        self.enable_filter = config.get('sensor.preprocessing.enable_filter', True)
        if self.enable_filter:
            self._setup_filter()
        
        # 异常值检测配置
        self.enable_outlier_detection = config.get('sensor.preprocessing.enable_outlier_detection', True)
        self.outlier_threshold = config.get('sensor.preprocessing.outlier_threshold', 3.0)
        
        # 历史数据窗口
        self.history_window = 10
        self.data_history = deque(maxlen=self.history_window)
        
        # 卡尔曼滤波器
        self._setup_kalman_filter()
    
    def _setup_filter(self) -> None:
        """设置数字滤波器"""
        filter_type = self.config.get('sensor.preprocessing.filter_type', 'butterworth')
        filter_order = self.config.get('sensor.preprocessing.filter_order', 2)
        cutoff_freq = self.config.get('sensor.preprocessing.cutoff_frequency', 10.0)
        sampling_rate = self.config.get('sensor.sampling_rate', 100)
        
        # 计算归一化截止频率
        nyquist_freq = sampling_rate / 2
        normalized_cutoff = cutoff_freq / nyquist_freq
        
        if filter_type == 'butterworth':
            self.filter_b, self.filter_a = signal.butter(
                filter_order, normalized_cutoff, btype='low'
            )
        else:
            raise ValueError(f"不支持的滤波器类型: {filter_type}")
        
        # 滤波器状态
        self.filter_zi = signal.lfilter_zi(self.filter_b, self.filter_a)
        self.filter_zi = np.tile(self.filter_zi[:, np.newaxis], (1, 12))  # 12DOF
    
    def _setup_kalman_filter(self) -> None:
        """设置卡尔曼滤波器"""
        # 为每个关节创建卡尔曼滤波器
        self.kalman_filters = []
        
        for i in range(12):  # 12DOF
            kf = KalmanFilter(dim_x=2, dim_z=1)
            
            # 状态转移矩阵 (位置和速度)
            dt = 1.0 / self.config.get('sensor.sampling_rate', 100)
            kf.F = np.array([[1., dt],
                            [0., 1.]])
            
            # 观测矩阵
            kf.H = np.array([[1., 0.]])
            
            # 过程噪声协方差
            kf.Q = np.array([[dt**4/4, dt**3/2],
                            [dt**3/2, dt**2]]) * 0.01
            
            # 观测噪声协方差
            kf.R = np.array([[0.1]])
            
            # 初始状态协方差
            kf.P *= 100
            
            self.kalman_filters.append(kf)
    
    def process(self, raw_data: np.ndarray) -> SensorData:
        """
        处理原始传感器数据
        
        Args:
            raw_data: 原始12DOF数据
            
        Returns:
            处理后的传感器数据
        """
        processed_data = raw_data.copy()
        quality = 1.0
        is_valid = True
        
        try:
            # 1. 异常值检测
            if self.enable_outlier_detection:
                processed_data, outlier_detected = self._detect_outliers(processed_data)
                if outlier_detected:
                    quality *= 0.8
                    self.logger.warning("检测到异常值，已进行处理")
            
            # 2. 数字滤波
            if self.enable_filter:
                processed_data = self._apply_digital_filter(processed_data)
            
            # 3. 卡尔曼滤波
            processed_data = self._apply_kalman_filter(processed_data)
            
            # 4. 数据验证
            is_valid = self._validate_data(processed_data)
            if not is_valid:
                quality = 0.0
                self.logger.error("数据验证失败")
            
            # 5. 更新历史数据
            self.data_history.append(processed_data.copy())
            
        except Exception as e:
            self.logger.error(f"数据预处理失败: {e}")
            is_valid = False
            quality = 0.0
        
        return SensorData(
            joint_angles=processed_data,
            timestamp=time.time(),
            quality=quality,
            is_valid=is_valid
        )
    
    def _detect_outliers(self, data: np.ndarray) -> tuple[np.ndarray, bool]:
        """
        检测和处理异常值
        
        Args:
            data: 输入数据
            
        Returns:
            处理后的数据和是否检测到异常值
        """
        if len(self.data_history) < 3:
            return data, False
        
        # 计算历史数据的统计信息
        history_array = np.array(list(self.data_history))
        mean_vals = np.mean(history_array, axis=0)
        std_vals = np.std(history_array, axis=0)
        
        # 检测异常值
        outlier_mask = np.abs(data - mean_vals) > (self.outlier_threshold * std_vals)
        outlier_detected = np.any(outlier_mask)
        
        if outlier_detected:
            # 用历史平均值替换异常值
            data[outlier_mask] = mean_vals[outlier_mask]
        
        return data, outlier_detected
    
    def _apply_digital_filter(self, data: np.ndarray) -> np.ndarray:
        """
        应用数字滤波器
        
        Args:
            data: 输入数据
            
        Returns:
            滤波后的数据
        """
        # 对每个关节分别滤波
        filtered_data = np.zeros_like(data)
        
        for i in range(len(data)):
            filtered_data[i], self.filter_zi[i] = signal.lfilter(
                self.filter_b, self.filter_a, [data[i]], zi=self.filter_zi[i]
            )
        
        return filtered_data.flatten()
    
    def _apply_kalman_filter(self, data: np.ndarray) -> np.ndarray:
        """
        应用卡尔曼滤波器
        
        Args:
            data: 输入数据
            
        Returns:
            滤波后的数据
        """
        filtered_data = np.zeros_like(data)
        
        for i in range(len(data)):
            # 预测
            self.kalman_filters[i].predict()
            
            # 更新
            self.kalman_filters[i].update(data[i])
            
            # 获取滤波后的位置
            filtered_data[i] = self.kalman_filters[i].x[0]
        
        return filtered_data
    
    def _validate_data(self, data: np.ndarray) -> bool:
        """
        验证数据有效性
        
        Args:
            data: 输入数据
            
        Returns:
            数据是否有效
        """
        # 检查数据范围
        if np.any(np.isnan(data)) or np.any(np.isinf(data)):
            return False
        
        # 检查关节角度范围 (-π 到 π)
        if np.any(np.abs(data) > np.pi):
            return False
        
        return True


class FiberOpticSensor(SensorInterface):
    """光纤传感器接口实现"""
    
    def __init__(self, config: ConfigManager, logger: Logger):
        """
        初始化光纤传感器
        
        Args:
            config: 配置管理器
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        
        # 串口配置
        self.port = config.get('sensor.port', '/dev/ttyUSB0')
        self.baudrate = config.get('sensor.baudrate', 115200)
        self.timeout = config.get('sensor.timeout', 0.01)
        
        # 连接状态
        self.serial_connection: Optional[serial.Serial] = None
        self.connected = False
        
        # 数据预处理器
        self.preprocessor = DataPreprocessor(config, logger)
        
        # 数据缓冲区
        self.data_buffer = deque(maxlen=100)
        self.read_lock = threading.Lock()
        
        # 校准参数
        self.calibration_data = self._load_calibration()
    
    def _load_calibration(self) -> Dict[str, Any]:
        """加载校准数据"""
        calibration_file = self.config.get('sensor.calibration_file')
        if calibration_file and os.path.exists(calibration_file):
            try:
                with open(calibration_file, 'r') as f:
                    return yaml.safe_load(f)
            except Exception as e:
                self.logger.warning(f"无法加载校准文件: {e}")
        
        # 默认校准参数
        return {
            'offset': np.zeros(12),
            'scale': np.ones(12),
            'rotation_matrix': np.eye(12)
        }
    
    def connect(self) -> bool:
        """
        连接光纤传感器
        
        Returns:
            连接是否成功
        """
        try:
            self.serial_connection = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE
            )
            
            # 等待连接稳定
            time.sleep(0.1)
            
            # 发送初始化命令
            self._send_init_commands()
            
            self.connected = True
            self.logger.info(f"光纤传感器连接成功: {self.port}")
            return True
            
        except Exception as e:
            self.logger.error(f"光纤传感器连接失败: {e}")
            self.connected = False
            return False
    
    def disconnect(self) -> None:
        """断开传感器连接"""
        if self.serial_connection and self.serial_connection.is_open:
            self.serial_connection.close()
        
        self.connected = False
        self.logger.info("光纤传感器已断开连接")
    
    def _send_init_commands(self) -> None:
        """发送初始化命令"""
        if not self.serial_connection:
            return
        
        # 发送启动命令
        init_commands = [
            b'START\r\n',
            b'MODE:CONTINUOUS\r\n',
            b'RATE:100\r\n'  # 100Hz采样率
        ]
        
        for cmd in init_commands:
            self.serial_connection.write(cmd)
            time.sleep(0.01)
    
    def read_data(self) -> Optional[SensorData]:
        """
        读取传感器数据
        
        Returns:
            传感器数据或None（如果读取失败）
        """
        if not self.is_connected():
            return None
        
        try:
            with self.read_lock:
                # 读取原始数据
                raw_data = self._read_raw_data()
                if raw_data is None:
                    return None
                
                # 应用校准
                calibrated_data = self._apply_calibration(raw_data)
                
                # 数据预处理
                processed_data = self.preprocessor.process(calibrated_data)
                
                return processed_data
                
        except Exception as e:
            self.logger.error(f"读取传感器数据失败: {e}")
            return None
    
    def _read_raw_data(self) -> Optional[np.ndarray]:
        """
        读取原始传感器数据
        
        Returns:
            12DOF原始数据或None
        """
        if not self.serial_connection or not self.serial_connection.is_open:
            return None
        
        try:
            # 读取一行数据
            line = self.serial_connection.readline().decode('utf-8').strip()
            
            if not line:
                return None
            
            # 解析数据格式: "angle1,angle2,...,angle12"
            values = line.split(',')
            if len(values) != 12:
                self.logger.warning(f"数据格式错误，期望12个值，实际{len(values)}个")
                return None
            
            # 转换为浮点数数组
            angles = np.array([float(v) for v in values])
            
            # 转换为弧度
            angles_rad = np.deg2rad(angles)
            
            return angles_rad
            
        except (ValueError, UnicodeDecodeError) as e:
            self.logger.warning(f"数据解析错误: {e}")
            return None
        except Exception as e:
            self.logger.error(f"读取原始数据失败: {e}")
            return None
    
    def _apply_calibration(self, raw_data: np.ndarray) -> np.ndarray:
        """
        应用校准参数
        
        Args:
            raw_data: 原始数据
            
        Returns:
            校准后的数据
        """
        # 应用偏移和缩放
        calibrated = (raw_data - self.calibration_data['offset']) * self.calibration_data['scale']
        
        # 应用旋转矩阵（如果需要坐标系转换）
        if 'rotation_matrix' in self.calibration_data:
            calibrated = self.calibration_data['rotation_matrix'] @ calibrated
        
        return calibrated
    
    def is_connected(self) -> bool:
        """
        检查连接状态
        
        Returns:
            是否已连接
        """
        return (self.connected and 
                self.serial_connection and 
                self.serial_connection.is_open)
    
    def get_sensor_info(self) -> Dict[str, Any]:
        """
        获取传感器信息
        
        Returns:
            传感器信息字典
        """
        return {
            'type': 'fiber_optic',
            'port': self.port,
            'baudrate': self.baudrate,
            'connected': self.is_connected(),
            'dof_count': 12,
            'sampling_rate': self.config.get('sensor.sampling_rate', 100)
        }
