"""
系统集成测试
"""

import unittest
import numpy as np
import time
import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.utils import <PERSON>fig<PERSON>ana<PERSON>, Logger
from src.sensor_interface import SensorData
from src.dip_estimator import DIPEstimator, DIPEstimation
from src.slsqp_optimizer import SLSQPOptimizer
from src.motion_mapper import MotionMapper


class MockSensorInterface:
    """模拟传感器接口"""
    
    def __init__(self):
        self.connected = False
        self.data_counter = 0
    
    def connect(self):
        self.connected = True
        return True
    
    def disconnect(self):
        self.connected = False
    
    def is_connected(self):
        return self.connected
    
    def read_data(self):
        """生成模拟传感器数据"""
        if not self.connected:
            return None
        
        # 生成12DOF模拟数据
        self.data_counter += 1
        t = self.data_counter * 0.02  # 50Hz
        
        # 模拟手指弯曲动作
        joint_angles = np.array([
            0.3 * np.sin(t),      # index_mcp
            0.5 * np.sin(t + 0.2), # index_pip
            0.3 * np.sin(t + 0.1), # middle_mcp
            0.5 * np.sin(t + 0.3), # middle_pip
            0.3 * np.sin(t + 0.2), # ring_mcp
            0.5 * np.sin(t + 0.4), # ring_pip
            0.3 * np.sin(t + 0.3), # pinky_mcp
            0.5 * np.sin(t + 0.5), # pinky_pip
            0.2 * np.sin(t),      # thumb_cmc_flex
            0.1 * np.sin(t + 0.1), # thumb_cmc_abd
            0.4 * np.sin(t + 0.2), # thumb_mcp
            0.3 * np.sin(t + 0.3), # thumb_ip
        ])
        
        return SensorData(
            joint_angles=joint_angles,
            timestamp=time.time(),
            quality=0.95,
            is_valid=True
        )


class TestSystemIntegration(unittest.TestCase):
    """系统集成测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        # 创建测试配置
        cls.config = ConfigManager('config.yaml')
        cls.logger = Logger('Test', cls.config)
        
        # 创建模拟传感器
        cls.mock_sensor = MockSensorInterface()
    
    def setUp(self):
        """每个测试方法的初始化"""
        self.mock_sensor.connect()
    
    def tearDown(self):
        """每个测试方法的清理"""
        self.mock_sensor.disconnect()
    
    def test_config_loading(self):
        """测试配置加载"""
        self.assertIsNotNone(self.config)
        self.assertEqual(self.config.get('robot_hand.dof_count'), 16)
        self.assertEqual(len(self.config.get('robot_hand.joint_names')), 16)
    
    def test_sensor_interface(self):
        """测试传感器接口"""
        # 测试连接
        self.assertTrue(self.mock_sensor.is_connected())
        
        # 测试数据读取
        data = self.mock_sensor.read_data()
        self.assertIsNotNone(data)
        self.assertTrue(data.is_valid)
        self.assertEqual(len(data.joint_angles), 12)
        self.assertGreater(data.quality, 0.5)
    
    def test_dip_estimator(self):
        """测试DIP关节估算器"""
        estimator = DIPEstimator(self.config, self.logger)
        
        # 生成测试数据
        sensor_data = np.random.uniform(-0.5, 0.5, 12)
        known_joints = np.random.uniform(-0.5, 0.5, 12)
        
        # 执行估算
        estimation = estimator.estimate_dip_joints(sensor_data, known_joints)
        
        # 验证结果
        self.assertIsInstance(estimation, DIPEstimation)
        self.assertEqual(len(estimation.dip_angles), 4)
        self.assertEqual(len(estimation.confidence), 4)
        self.assertTrue(all(0 <= c <= 1 for c in estimation.confidence))
        self.assertTrue(all(0 <= angle <= np.pi/2 for angle in estimation.dip_angles))
    
    def test_slsqp_optimizer(self):
        """测试SLSQP优化器"""
        optimizer = SLSQPOptimizer(self.config, self.logger)
        
        # 生成测试数据
        initial_guess = np.random.uniform(-0.5, 0.5, 16)
        human_keypoints = {
            'wrist': np.array([0.0, 0.0, 0.0]),
            'thumb_tip': np.array([0.05, -0.03, 0.02]),
            'index_tip': np.array([0.08, 0.02, 0.01]),
            'middle_tip': np.array([0.09, 0.0, 0.01]),
            'ring_tip': np.array([0.08, -0.02, 0.01]),
            'pinky_tip': np.array([0.07, -0.04, 0.01])
        }
        sensor_data = np.random.uniform(-0.5, 0.5, 12)
        
        # 执行优化
        result = optimizer.optimize(initial_guess, human_keypoints, sensor_data)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(len(result.optimized_joints), 16)
        self.assertGreater(result.computation_time, 0)
        self.assertGreaterEqual(result.iterations, 0)
    
    def test_motion_mapper(self):
        """测试运动映射器"""
        mapper = MotionMapper(self.config, self.logger)
        
        # 生成测试传感器数据
        sensor_data = self.mock_sensor.read_data()
        
        # 执行映射
        start_time = time.time()
        result = mapper.map_motion(sensor_data)
        computation_time = time.time() - start_time
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(len(result.robot_joints), 16)
        self.assertGreater(result.mapping_quality, 0)
        self.assertLess(computation_time, 0.1)  # 应该在100ms内完成
        
        # 验证关节角度范围
        for i, angle in enumerate(result.robot_joints):
            self.assertFalse(np.isnan(angle), f"关节{i}角度为NaN")
            self.assertFalse(np.isinf(angle), f"关节{i}角度为无穷大")
    
    def test_performance_requirements(self):
        """测试性能要求"""
        mapper = MotionMapper(self.config, self.logger)
        
        # 测试多次映射的性能
        computation_times = []
        mapping_qualities = []
        
        for i in range(50):  # 测试50次
            sensor_data = self.mock_sensor.read_data()
            
            start_time = time.time()
            result = mapper.map_motion(sensor_data)
            computation_time = time.time() - start_time
            
            computation_times.append(computation_time)
            mapping_qualities.append(result.mapping_quality)
        
        # 验证性能指标
        avg_computation_time = np.mean(computation_times)
        max_computation_time = np.max(computation_times)
        avg_quality = np.mean(mapping_qualities)
        
        self.assertLess(avg_computation_time, 0.01, 
                       f"平均计算时间{avg_computation_time:.4f}s超过10ms要求")
        self.assertLess(max_computation_time, 0.02, 
                       f"最大计算时间{max_computation_time:.4f}s超过20ms要求")
        self.assertGreater(avg_quality, 0.8, 
                          f"平均映射质量{avg_quality:.3f}低于0.8要求")
        
        # 计算理论最大频率
        theoretical_max_freq = 1.0 / max_computation_time
        self.assertGreater(theoretical_max_freq, 50, 
                          f"理论最大频率{theoretical_max_freq:.1f}Hz低于50Hz要求")
    
    def test_error_handling(self):
        """测试错误处理"""
        mapper = MotionMapper(self.config, self.logger)
        
        # 测试无效传感器数据
        invalid_data = SensorData(
            joint_angles=np.array([np.nan] * 12),
            timestamp=time.time(),
            quality=0.0,
            is_valid=False
        )
        
        result = mapper.map_motion(invalid_data)
        self.assertIsNotNone(result)
        self.assertEqual(result.mapping_quality, 0.0)
        
        # 测试极端输入值
        extreme_data = SensorData(
            joint_angles=np.array([10.0] * 12),  # 极大值
            timestamp=time.time(),
            quality=1.0,
            is_valid=True
        )
        
        result = mapper.map_motion(extreme_data)
        self.assertIsNotNone(result)
        # 结果应该被限制在合理范围内
        for angle in result.robot_joints:
            self.assertGreater(angle, -np.pi)
            self.assertLess(angle, np.pi)
    
    def test_consistency(self):
        """测试一致性"""
        mapper = MotionMapper(self.config, self.logger)
        
        # 使用相同输入测试多次
        sensor_data = self.mock_sensor.read_data()
        
        results = []
        for i in range(5):
            result = mapper.map_motion(sensor_data)
            results.append(result.robot_joints)
        
        # 验证结果一致性（应该非常接近）
        for i in range(1, len(results)):
            diff = np.abs(results[i] - results[0])
            max_diff = np.max(diff)
            self.assertLess(max_diff, 0.01, 
                           f"相同输入的结果差异{max_diff:.4f}过大")
    
    def test_temporal_smoothness(self):
        """测试时间平滑性"""
        mapper = MotionMapper(self.config, self.logger)
        
        # 生成连续的传感器数据
        previous_result = None
        max_velocity = 0.0
        
        for i in range(20):
            sensor_data = self.mock_sensor.read_data()
            result = mapper.map_motion(sensor_data)
            
            if previous_result is not None:
                # 计算关节速度（假设50Hz）
                dt = 0.02
                velocity = np.abs(result.robot_joints - previous_result.robot_joints) / dt
                max_joint_velocity = np.max(velocity)
                max_velocity = max(max_velocity, max_joint_velocity)
            
            previous_result = result
        
        # 验证最大速度在合理范围内（<π rad/s = 180°/s）
        self.assertLess(max_velocity, np.pi, 
                       f"最大关节速度{np.rad2deg(max_velocity):.1f}°/s超过180°/s限制")


class TestSystemBenchmark(unittest.TestCase):
    """系统性能基准测试"""
    
    def setUp(self):
        """初始化"""
        self.config = ConfigManager('config.yaml')
        self.logger = Logger('Benchmark', self.config)
        self.mock_sensor = MockSensorInterface()
        self.mock_sensor.connect()
        self.mapper = MotionMapper(self.config, self.logger)
    
    def test_throughput_benchmark(self):
        """吞吐量基准测试"""
        print("\n=== 吞吐量基准测试 ===")
        
        num_iterations = 1000
        start_time = time.time()
        
        for i in range(num_iterations):
            sensor_data = self.mock_sensor.read_data()
            result = self.mapper.map_motion(sensor_data)
        
        total_time = time.time() - start_time
        throughput = num_iterations / total_time
        
        print(f"总时间: {total_time:.2f}秒")
        print(f"处理帧数: {num_iterations}")
        print(f"吞吐量: {throughput:.1f} FPS")
        print(f"平均延迟: {1000/throughput:.2f}ms")
        
        # 验证性能要求
        self.assertGreater(throughput, 50, f"吞吐量{throughput:.1f}FPS低于50FPS要求")
    
    def test_latency_distribution(self):
        """延迟分布测试"""
        print("\n=== 延迟分布测试 ===")
        
        latencies = []
        
        for i in range(500):
            sensor_data = self.mock_sensor.read_data()
            
            start_time = time.time()
            result = self.mapper.map_motion(sensor_data)
            latency = time.time() - start_time
            
            latencies.append(latency * 1000)  # 转换为毫秒
        
        latencies = np.array(latencies)
        
        print(f"平均延迟: {np.mean(latencies):.2f}ms")
        print(f"中位数延迟: {np.median(latencies):.2f}ms")
        print(f"95%分位延迟: {np.percentile(latencies, 95):.2f}ms")
        print(f"99%分位延迟: {np.percentile(latencies, 99):.2f}ms")
        print(f"最大延迟: {np.max(latencies):.2f}ms")
        
        # 验证延迟要求
        self.assertLess(np.mean(latencies), 10, 
                       f"平均延迟{np.mean(latencies):.2f}ms超过10ms要求")
        self.assertLess(np.percentile(latencies, 95), 20, 
                       f"95%分位延迟{np.percentile(latencies, 95):.2f}ms超过20ms要求")


def run_tests():
    """运行所有测试"""
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestSystemIntegration))
    suite.addTests(loader.loadTestsFromTestCase(TestSystemBenchmark))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
