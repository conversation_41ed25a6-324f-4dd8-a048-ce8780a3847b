import numpy as np
import matplotlib.pyplot as plt

# 激活函数对比实验
def compare_activation_functions():
    """对比不同激活函数的特性"""
    z = np.linspace(-5, 5, 100)
    
    # 计算各种激活函数
    sigmoid_vals = 1 / (1 + np.exp(-z))
    tanh_vals = np.tanh(z)
    relu_vals = np.maximum(0, z)
    
    # 计算导数
    sigmoid_grad = sigmoid_vals * (1 - sigmoid_vals)
    tanh_grad = 1 - tanh_vals**2
    relu_grad = (z > 0).astype(float)
    
    # 绘图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
    
    # 激活函数
    ax1.plot(z, sigmoid_vals, label='Sigmoid', color='blue')
    ax1.plot(z, tanh_vals, label='Tanh', color='red')
    ax1.plot(z, relu_vals, label='ReLU', color='green')
    ax1.set_title('激活函数对比')
    ax1.set_xlabel('z')
    ax1.set_ylabel('activation(z)')
    ax1.legend()
    ax1.grid(True)
    
    # 导数
    ax2.plot(z, sigmoid_grad, label='Sigmoid导数', color='blue')
    ax2.plot(z, tanh_grad, label='Tanh导数', color='red')
    ax2.plot(z, relu_grad, label='ReLU导数', color='green')
    ax2.set_title('激活函数导数对比')
    ax2.set_xlabel('z')
    ax2.set_ylabel("activation'(z)")
    ax2.legend()
    ax2.grid(True)
    
    # 梯度消失问题演示
    layers = np.arange(1, 11)
    sigmoid_grad_product = np.cumprod([0.25] * 10)  # sigmoid最大梯度0.25
    tanh_grad_product = np.cumprod([1.0] * 10)      # tanh最大梯度1.0
    relu_grad_product = np.ones(10)                 # ReLU梯度1.0
    
    ax3.semilogy(layers, sigmoid_grad_product, 'o-', label='Sigmoid', color='blue')
    ax3.semilogy(layers, tanh_grad_product, 's-', label='Tanh', color='red')
    ax3.semilogy(layers, relu_grad_product, '^-', label='ReLU', color='green')
    ax3.set_title('梯度在深层网络中的传播')
    ax3.set_xlabel('网络层数')
    ax3.set_ylabel('梯度大小 (对数尺度)')
    ax3.legend()
    ax3.grid(True)
    
    # 不同初始化方法的影响
    layer_sizes = [100, 50, 25, 10]
    
    # Xavier初始化
    xavier_vars = []
    for i in range(len(layer_sizes)-1):
        var = 1.0 / layer_sizes[i]
        xavier_vars.append(var)
    
    # He初始化
    he_vars = []
    for i in range(len(layer_sizes)-1):
        var = 2.0 / layer_sizes[i]
        he_vars.append(var)
    
    ax4.plot(range(1, len(layer_sizes)), xavier_vars, 'o-', label='Xavier初始化', color='blue')
    ax4.plot(range(1, len(layer_sizes)), he_vars, 's-', label='He初始化', color='red')
    ax4.set_title('不同初始化方法的权重方差')
    ax4.set_xlabel('层数')
    ax4.set_ylabel('权重初始化方差')
    ax4.legend()
    ax4.grid(True)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    compare_activation_functions()