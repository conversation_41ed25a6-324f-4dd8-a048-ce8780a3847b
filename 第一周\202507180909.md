吴恩达机器学习第三天
梯度下降 - 最小化成本函数

1. w = w - α * (∂/∂w)J(w,b)
2. b = b - α * (∂/∂b)J(w,b)

重点：注意同步更新问题。

```python
# 正确实现，防止不同步更新
temp_w = w - alpha * dw
temp_b = b - alpha * db

w = temp_w
b = temp_b
```

梯度下降，其实就是在对对应的参数求偏导，找最快下降代价函数的方向，以期最快到达最小代价点

```python
# 计算梯度的函数
def compute_gradient(x, y, w, b): 
    """
    Computes the gradient for linear regression 
    Args:
      x (ndarray (m,)): Data, m examples 
      y (ndarray (m,)): target values
      w,b (scalar)    : model parameters  
    Returns
      dj_dw (scalar): The gradient of the cost w.r.t. the parameters w
      dj_db (scalar): The gradient of the cost w.r.t. the parameter b     
     """
    
    # Number of training examples
    m = x.shape[0]    
    dj_dw = 0
    dj_db = 0
    
    for i in range(m):  
        f_wb = w * x[i] + b 
        dj_dw_i = (f_wb - y[i]) * x[i] // 这里进行了数学的偏导数运算
        dj_db_i = f_wb - y[i] 
        dj_db += dj_db_i
        dj_dw += dj_dw_i 
    dj_dw = dj_dw / m 
    dj_db = dj_db / m 
        
    return dj_dw, dj_db
```

关于梯度下降部分代码有趣的部分
w = copy.deepcopy(w_in) # 深度拷贝，防止全局的w_in被修改
但是深拷贝会增加很多开销，默认用普通赋值，防止变量重复定义即可

