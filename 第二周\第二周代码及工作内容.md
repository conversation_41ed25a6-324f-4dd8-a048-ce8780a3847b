第2周 7.21-7.27
周总结
- 做对了什么
- 收获了什么经验
- 做错了什么
- 有哪些要避免的教训
本周的技术栈有所变化PyQt5 + OpenGL + 数据处理库 + Mujoco原生，并采用git进行版本管理。这周所做的工作更多是一个小模块，比如在设备的识别和数据的格式输出等方向上，加深了对于设备识别和检测的理解，但是纯在的问题就是逻辑的优化上，如何达成自动扫描和手动的平衡，防止持续扫描造成大量的开销。仍然需要坚持阅读论文，了解更多的方法来解决当前很多遇到的问题和困境。

7月21日
主要工作产出
1. imu_sdk 优化：修复openGL的形变问题，如果超过人体活动关节范围，则限定最大值防止人物身体部位脱离任务
2. 尝试将获取四元数的程序和使用四元数的模型渲染程序融合在一起
明日计划
[x] 尝试更换仿真的方案，使用mujoco
[x] 尝试将IMU SDK代码集成到一块

---
7月22日
主要工作产出
1. mujoco仿真尝试：成功将官方提供的humanixod.xml模型导入项目中
2. 提示词工程：构建一版专业、简洁的提示词
3. 界面修改：将imu的导入和渲染放入一个界面
明日计划
[] 解决imu当前的映射问题
[x] 寻找一个比较完善的mujoco人体模型
[x] 帮助学弟构建casia知识库
[x] 修改使用流程中违反直觉的逻辑

---

7月23日
主要工作产出
1. mujoco仿真尝试：使用官方mujoco界面嵌入Qt界面
2. 界面修改：将imu的识别和mujoco界面渲染放入同一个Qt
3. 映射控制修改：设置一个函数修改imu的关节映射
[图片]
明日计划
[] 解决imu当前的关节控制问题
[x] imu活跃设备识别的问题

---
7月24日
主要工作产出
1. 活跃设备识别的问题：输出COM9端口的数据格式，然后自动根据数据格式筛选符合要求的端口，自动连接，但是还有逻辑问题，界面加载的速度比较慢
2. 路由器安装
3. 一个临时邮件网站的搭建：Cloudflare + digitalPlat 白嫖党赚麻了，主要设置一个邮件路由规则（没什么用
明日计划
[] 解决imu当前的关节控制问题
[] 解决界面加载缓慢问题

---
7月25日
主要工作产出
1. 活跃设备识别的问题：再次优化com9端口的识别问题，写了一个脚本文件，输出com9端口的所有内容
2. XL330舵机实测：主要是舵机不能串联，以及需要改线（实际只要把电接上就可以动
- 软件：R+ manage 2.0，需要修改equre enable，然后就可以直接操作goal position
[图片]
3. 阅读一篇论文，LLM+点云，对物品进行类别的划分以及中心点TCP的识别，根据语言模型给出的语义标签，对于特定功能生成相应的抓握姿势。（遮挡问题依然是重头戏，似乎并没有解决且忽略了这个问题
明日计划
[] 解决imu当前的关节控制问题
[] 解决界面加载缓慢问题

---