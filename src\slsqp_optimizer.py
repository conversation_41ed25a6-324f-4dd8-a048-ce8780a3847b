"""
SLSQP优化器模块
实现Sequential Least-Squares Quadratic Programming算法
用于手部运动映射的实时优化
"""

import time
import numpy as np
import nlopt
from scipy.optimize import minimize
from typing import Dict, Any, List, Tuple, Optional, Callable
from dataclasses import dataclass
from abc import ABC, abstractmethod

from .utils import ConfigManager, Logger, degrees_to_radians, radians_to_degrees


@dataclass
class OptimizationResult:
    """优化结果数据类"""
    optimized_joints: np.ndarray    # 优化后的关节角度
    objective_value: float          # 目标函数值
    success: bool                   # 优化是否成功
    iterations: int                 # 迭代次数
    computation_time: float         # 计算时间
    convergence_info: str           # 收敛信息


@dataclass
class KeypointVector:
    """关键点向量定义"""
    start_point: str               # 起始点名称
    end_point: str                 # 终止点名称
    weight: float                  # 权重
    adaptive_weight: bool = True   # 是否使用自适应权重


class ForwardKinematics:
    """正向运动学计算器"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化正向运动学
        
        Args:
            config: 配置管理器
        """
        self.config = config
        self.joint_names = config.get('robot_hand.joint_names', [])
        
        # 手部几何参数 (单位: 米)
        self.hand_geometry = {
            'palm_length': 0.10,
            'palm_width': 0.08,
            'finger_lengths': {
                'thumb': [0.04, 0.03, 0.025],      # 拇指各段长度
                'index': [0.045, 0.025, 0.02],     # 食指各段长度
                'middle': [0.05, 0.028, 0.022],    # 中指各段长度
                'ring': [0.048, 0.026, 0.021],     # 无名指各段长度
                'pinky': [0.04, 0.022, 0.018]      # 小指各段长度
            }
        }
        
        # 关键点定义
        self.keypoints = [
            'wrist', 'thumb_tip', 'index_tip', 'middle_tip', 'ring_tip', 'pinky_tip'
        ]
    
    def compute_keypoint_positions(self, joint_angles: np.ndarray) -> Dict[str, np.ndarray]:
        """
        计算关键点位置
        
        Args:
            joint_angles: 16DOF关节角度
            
        Returns:
            关键点位置字典
        """
        positions = {}
        
        # 手腕位置（参考原点）
        positions['wrist'] = np.array([0.0, 0.0, 0.0])
        
        # 计算各手指指尖位置
        positions['thumb_tip'] = self._compute_thumb_tip(joint_angles)
        positions['index_tip'] = self._compute_finger_tip('index', joint_angles)
        positions['middle_tip'] = self._compute_finger_tip('middle', joint_angles)
        positions['ring_tip'] = self._compute_finger_tip('ring', joint_angles)
        positions['pinky_tip'] = self._compute_finger_tip('pinky', joint_angles)
        
        return positions
    
    def _compute_thumb_tip(self, joint_angles: np.ndarray) -> np.ndarray:
        """计算拇指指尖位置"""
        # 拇指关节角度
        cmc_flex = joint_angles[0]    # 腕掌关节屈伸
        cmc_abd = joint_angles[1]     # 腕掌关节外展
        mcp = joint_angles[2]         # 掌指关节
        ip = joint_angles[3]          # 指间关节
        
        # 拇指基础位置（相对于手腕）
        base_pos = np.array([0.02, -0.03, 0.0])
        
        # 计算各段变换
        lengths = self.hand_geometry['finger_lengths']['thumb']
        
        # CMC关节变换
        cmc_transform = self._rotation_matrix_z(cmc_abd) @ self._rotation_matrix_y(cmc_flex)
        
        # 第一段（掌骨）
        segment1 = cmc_transform @ np.array([lengths[0], 0, 0])
        
        # MCP关节变换
        mcp_transform = cmc_transform @ self._rotation_matrix_y(mcp)
        segment2 = mcp_transform @ np.array([lengths[1], 0, 0])
        
        # IP关节变换
        ip_transform = mcp_transform @ self._rotation_matrix_y(ip)
        segment3 = ip_transform @ np.array([lengths[2], 0, 0])
        
        # 最终位置
        tip_position = base_pos + segment1 + segment2 + segment3
        
        return tip_position
    
    def _compute_finger_tip(self, finger: str, joint_angles: np.ndarray) -> np.ndarray:
        """计算手指指尖位置"""
        # 获取关节索引
        joint_indices = self._get_finger_joint_indices(finger)
        
        # 关节角度
        mcp = joint_angles[joint_indices['mcp']]
        pip = joint_angles[joint_indices['pip']]
        dip = joint_angles[joint_indices['dip']]
        
        # 手指基础位置
        base_positions = {
            'index': np.array([0.0, 0.02, 0.0]),
            'middle': np.array([0.0, 0.0, 0.0]),
            'ring': np.array([0.0, -0.02, 0.0]),
            'pinky': np.array([0.0, -0.04, 0.0])
        }
        base_pos = base_positions[finger]
        
        # 手指段长度
        lengths = self.hand_geometry['finger_lengths'][finger]
        
        # 计算各段变换
        # 第一段（掌骨）
        mcp_transform = self._rotation_matrix_y(mcp)
        segment1 = mcp_transform @ np.array([lengths[0], 0, 0])
        
        # 第二段（近节指骨）
        pip_transform = mcp_transform @ self._rotation_matrix_y(pip)
        segment2 = pip_transform @ np.array([lengths[1], 0, 0])
        
        # 第三段（远节指骨）
        dip_transform = pip_transform @ self._rotation_matrix_y(dip)
        segment3 = dip_transform @ np.array([lengths[2], 0, 0])
        
        # 最终位置
        tip_position = base_pos + segment1 + segment2 + segment3
        
        return tip_position
    
    def _get_finger_joint_indices(self, finger: str) -> Dict[str, int]:
        """获取手指关节索引"""
        indices = {
            'index': {'mcp': 4, 'pip': 5, 'dip': 6},
            'middle': {'mcp': 7, 'pip': 8, 'dip': 9},
            'ring': {'mcp': 10, 'pip': 11, 'dip': 12},
            'pinky': {'mcp': 13, 'pip': 14, 'dip': 15}
        }
        return indices[finger]
    
    def _rotation_matrix_y(self, angle: float) -> np.ndarray:
        """绕Y轴旋转矩阵"""
        c = np.cos(angle)
        s = np.sin(angle)
        return np.array([
            [c, 0, s],
            [0, 1, 0],
            [-s, 0, c]
        ])
    
    def _rotation_matrix_z(self, angle: float) -> np.ndarray:
        """绕Z轴旋转矩阵"""
        c = np.cos(angle)
        s = np.sin(angle)
        return np.array([
            [c, -s, 0],
            [s, c, 0],
            [0, 0, 1]
        ])


class SLSQPOptimizer:
    """SLSQP优化器主类"""
    
    def __init__(self, config: ConfigManager, logger: Logger):
        """
        初始化SLSQP优化器
        
        Args:
            config: 配置管理器
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        
        # 优化参数
        self.ftol = config.get('slsqp_optimizer.ftol', 1e-6)
        self.xtol = config.get('slsqp_optimizer.xtol', 1e-6)
        self.maxiter = config.get('slsqp_optimizer.maxiter', 100)
        self.finite_diff_rel_step = config.get('slsqp_optimizer.finite_diff_rel_step', 1e-8)
        
        # 权重配置
        self.weights = config.get('slsqp_optimizer.weights', {})
        
        # 关键向量配置
        keypoint_vector_configs = config.get('slsqp_optimizer.keypoint_vectors', [])
        self.keypoint_vectors = self._create_keypoint_vectors(keypoint_vector_configs)
        
        # 正向运动学
        self.forward_kinematics = ForwardKinematics(config)
        
        # 关节限制
        self.joint_limits = self._load_joint_limits()
        
        # 历史数据
        self.previous_joints: Optional[np.ndarray] = None
        self.previous_human_keypoints: Optional[Dict[str, np.ndarray]] = None
        
        # 性能统计
        self.optimization_stats = {
            'total_optimizations': 0,
            'successful_optimizations': 0,
            'average_computation_time': 0.0,
            'average_iterations': 0.0
        }
    
    def _create_keypoint_vectors(self, configs: List[List[str]]) -> List[KeypointVector]:
        """创建关键点向量列表"""
        vectors = []
        default_weight = 1.0
        
        for config in configs:
            if len(config) >= 2:
                start_point = config[0]
                end_point = config[1]
                weight = config[2] if len(config) > 2 else default_weight
                
                vectors.append(KeypointVector(
                    start_point=start_point,
                    end_point=end_point,
                    weight=weight,
                    adaptive_weight=True
                ))
        
        return vectors
    
    def _load_joint_limits(self) -> List[Tuple[float, float]]:
        """加载关节限制"""
        joint_limits_config = self.config.get('robot_hand.joint_limits', {})
        joint_names = self.config.get('robot_hand.joint_names', [])
        
        limits = []
        for joint_name in joint_names:
            if joint_name in joint_limits_config:
                min_deg, max_deg = joint_limits_config[joint_name]
                limits.append((np.deg2rad(min_deg), np.deg2rad(max_deg)))
            else:
                # 默认限制
                limits.append((-np.pi, np.pi))
        
        return limits
    
    def optimize(self, initial_guess: np.ndarray, human_keypoints: Dict[str, np.ndarray],
                sensor_data: np.ndarray) -> OptimizationResult:
        """
        执行SLSQP优化
        
        Args:
            initial_guess: 初始关节角度猜测
            human_keypoints: 人手关键点位置
            sensor_data: 传感器数据
            
        Returns:
            优化结果
        """
        start_time = time.time()
        
        try:
            # 定义目标函数
            def objective(x):
                return self._compute_objective_function(x, human_keypoints, sensor_data)
            
            # 定义约束
            constraints = self._create_constraints()
            
            # 定义边界
            bounds = [(limit[0], limit[1]) for limit in self.joint_limits]
            
            # 执行优化
            result = minimize(
                objective,
                initial_guess,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={
                    'ftol': self.ftol,
                    'maxiter': self.maxiter,
                    'disp': False,
                    'finite_diff_rel_step': self.finite_diff_rel_step
                }
            )
            
            computation_time = time.time() - start_time
            
            # 更新统计信息
            self._update_stats(result.success, computation_time, result.nit)
            
            # 更新历史数据
            if result.success:
                self.previous_joints = result.x.copy()
                self.previous_human_keypoints = human_keypoints.copy()
            
            return OptimizationResult(
                optimized_joints=result.x,
                objective_value=result.fun,
                success=result.success,
                iterations=result.nit,
                computation_time=computation_time,
                convergence_info=result.message
            )
            
        except Exception as e:
            computation_time = time.time() - start_time
            self.logger.error(f"SLSQP优化失败: {e}")
            
            return OptimizationResult(
                optimized_joints=initial_guess,
                objective_value=float('inf'),
                success=False,
                iterations=0,
                computation_time=computation_time,
                convergence_info=f"优化异常: {str(e)}"
            )
    
    def _compute_objective_function(self, robot_joints: np.ndarray, 
                                  human_keypoints: Dict[str, np.ndarray],
                                  sensor_data: np.ndarray) -> float:
        """
        计算目标函数值
        
        Args:
            robot_joints: 机器人关节角度
            human_keypoints: 人手关键点
            sensor_data: 传感器数据
            
        Returns:
            目标函数值
        """
        total_cost = 0.0
        
        try:
            # 1. 关键点匹配误差
            keypoint_cost = self._compute_keypoint_matching_cost(robot_joints, human_keypoints)
            total_cost += self.weights.get('keypoint_matching', 1.0) * keypoint_cost
            
            # 2. 时间平滑约束
            if self.previous_joints is not None:
                temporal_cost = self._compute_temporal_smoothness_cost(robot_joints)
                total_cost += self.weights.get('temporal_smooth', 0.1) * temporal_cost
            
            # 3. DIP关节生物力学约束
            dip_cost = self._compute_dip_constraint_cost(robot_joints)
            total_cost += self.weights.get('dip_constraint', 0.5) * dip_cost
            
            # 4. 关节限制软约束
            joint_limit_cost = self._compute_joint_limit_penalty(robot_joints)
            total_cost += self.weights.get('joint_limit', 10.0) * joint_limit_cost
            
            # 5. 生物力学约束
            biomechanical_cost = self._compute_biomechanical_cost(robot_joints)
            total_cost += self.weights.get('biomechanical', 2.0) * biomechanical_cost
            
            # 6. 碰撞避免约束
            collision_cost = self._compute_collision_avoidance_cost(robot_joints)
            total_cost += self.weights.get('collision_avoidance', 5.0) * collision_cost
            
        except Exception as e:
            self.logger.warning(f"目标函数计算异常: {e}")
            total_cost = 1e6  # 返回大值表示无效解
        
        return total_cost
    
    def _compute_keypoint_matching_cost(self, robot_joints: np.ndarray,
                                      human_keypoints: Dict[str, np.ndarray]) -> float:
        """计算关键点匹配误差"""
        # 计算机器人关键点位置
        robot_keypoints = self.forward_kinematics.compute_keypoint_positions(robot_joints)
        
        total_error = 0.0
        
        for vector in self.keypoint_vectors:
            if (vector.start_point in human_keypoints and 
                vector.end_point in human_keypoints and
                vector.start_point in robot_keypoints and 
                vector.end_point in robot_keypoints):
                
                # 计算人手向量
                human_vector = human_keypoints[vector.end_point] - human_keypoints[vector.start_point]
                
                # 计算机器人向量
                robot_vector = robot_keypoints[vector.end_point] - robot_keypoints[vector.start_point]
                
                # 计算向量差异
                vector_error = np.linalg.norm(human_vector - robot_vector)
                
                # 应用权重
                weight = vector.weight
                if vector.adaptive_weight:
                    weight *= self._compute_adaptive_weight(human_vector)
                
                total_error += weight * vector_error ** 2
        
        return total_error
    
    def _compute_adaptive_weight(self, vector: np.ndarray) -> float:
        """计算自适应权重"""
        # 基于向量长度的自适应权重
        vector_length = np.linalg.norm(vector)
        
        if vector_length < 0.01:  # 很短的向量
            return 0.5
        elif vector_length > 0.15:  # 很长的向量
            return 1.5
        else:
            return 1.0
    
    def _compute_temporal_smoothness_cost(self, robot_joints: np.ndarray) -> float:
        """计算时间平滑约束"""
        if self.previous_joints is None:
            return 0.0
        
        joint_velocity = robot_joints - self.previous_joints
        return np.sum(joint_velocity ** 2)
    
    def _compute_dip_constraint_cost(self, robot_joints: np.ndarray) -> float:
        """计算DIP关节约束"""
        cost = 0.0
        
        # DIP关节索引
        dip_indices = [6, 9, 12, 15]  # index, middle, ring, pinky DIP
        pip_indices = [5, 8, 11, 14]  # 对应的PIP关节
        
        for dip_idx, pip_idx in zip(dip_indices, pip_indices):
            dip_angle = robot_joints[dip_idx]
            pip_angle = robot_joints[pip_idx]
            
            # DIP不应超过PIP的80%
            if dip_angle > 0.8 * pip_angle:
                cost += (dip_angle - 0.8 * pip_angle) ** 2
        
        return cost
    
    def _compute_joint_limit_penalty(self, robot_joints: np.ndarray) -> float:
        """计算关节限制惩罚"""
        penalty = 0.0
        
        for i, (min_limit, max_limit) in enumerate(self.joint_limits):
            if robot_joints[i] < min_limit:
                penalty += (min_limit - robot_joints[i]) ** 2
            elif robot_joints[i] > max_limit:
                penalty += (robot_joints[i] - max_limit) ** 2
        
        return penalty
    
    def _compute_biomechanical_cost(self, robot_joints: np.ndarray) -> float:
        """计算生物力学约束"""
        cost = 0.0
        
        # 相邻手指协调约束
        finger_mcp_indices = [4, 7, 10, 13]  # index, middle, ring, pinky MCP
        
        for i in range(len(finger_mcp_indices) - 1):
            angle_diff = abs(robot_joints[finger_mcp_indices[i]] - 
                           robot_joints[finger_mcp_indices[i + 1]])
            
            # 相邻手指角度差不应超过45度
            if angle_diff > np.pi / 4:
                cost += (angle_diff - np.pi / 4) ** 2
        
        return cost
    
    def _compute_collision_avoidance_cost(self, robot_joints: np.ndarray) -> float:
        """计算碰撞避免约束"""
        # 简化的碰撞检测：检查手指间的最小距离
        cost = 0.0
        
        # 计算指尖位置
        keypoints = self.forward_kinematics.compute_keypoint_positions(robot_joints)
        
        finger_tips = ['thumb_tip', 'index_tip', 'middle_tip', 'ring_tip', 'pinky_tip']
        min_distance = 0.02  # 最小距离2cm
        
        for i in range(len(finger_tips)):
            for j in range(i + 1, len(finger_tips)):
                tip1 = keypoints[finger_tips[i]]
                tip2 = keypoints[finger_tips[j]]
                distance = np.linalg.norm(tip1 - tip2)
                
                if distance < min_distance:
                    cost += (min_distance - distance) ** 2
        
        return cost
    
    def _create_constraints(self) -> List[Dict]:
        """创建优化约束"""
        constraints = []
        
        # 关节限制约束已通过bounds处理
        # 这里可以添加其他等式或不等式约束
        
        return constraints
    
    def _update_stats(self, success: bool, computation_time: float, iterations: int) -> None:
        """更新优化统计信息"""
        self.optimization_stats['total_optimizations'] += 1
        
        if success:
            self.optimization_stats['successful_optimizations'] += 1
        
        # 更新平均值
        total = self.optimization_stats['total_optimizations']
        self.optimization_stats['average_computation_time'] = (
            (self.optimization_stats['average_computation_time'] * (total - 1) + computation_time) / total
        )
        self.optimization_stats['average_iterations'] = (
            (self.optimization_stats['average_iterations'] * (total - 1) + iterations) / total
        )
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        stats = self.optimization_stats.copy()
        
        if stats['total_optimizations'] > 0:
            stats['success_rate'] = (stats['successful_optimizations'] / 
                                   stats['total_optimizations'])
        else:
            stats['success_rate'] = 0.0
        
        return stats
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.optimization_stats = {
            'total_optimizations': 0,
            'successful_optimizations': 0,
            'average_computation_time': 0.0,
            'average_iterations': 0.0
        }
